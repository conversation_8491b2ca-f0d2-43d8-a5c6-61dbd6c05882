<!DOCTYPE html>
<html>
<head>
    <title>特定Mermaid错误修复测试</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
</head>
<body>
    <h2>原始错误格式（用户提供的）</h2>
    <div id="original"></div>
    
    <h2>修复后格式</h2>
    <div id="fixed"></div>
    
    <script>
        mermaid.initialize({ startOnLoad: false, logLevel: 'debug' });
        
        // 用户提供的原始代码（会出错）
        const originalCode = `mermaidflowchart
 B --> B1[潜在供应商寻源]
 B --> B2[资质文件初审]
 B --> B3[现场考察评估]
 B --> B4[准入评审会]
 B4 --> C{是否列入合格名录?}
 C -->|是| D[招标竞争阶段]
 C -->|否| B1 D --> D1[采购需求分析]`;
        
        // 修复后的代码
        const fixedCode = `flowchart TD
    A[开始] --> B[资质评估]
    B --> B1[潜在供应商寻源]
    B --> B2[资质文件初审]
    B --> B3[现场考察评估]
    B --> B4[准入评审会]
    B4 --> C{是否列入合格名录?}
    C -->|是| D[招标竞争阶段]
    C -->|否| B1
    D --> D1[采购需求分析]`;
        
        // 测试原始代码
        document.getElementById('original').innerHTML = 
            '<h3>原始代码（会出错）:</h3><pre style="background: #ffe6e6; padding: 10px; border: 1px solid #ff0000;">' + 
            originalCode + '</pre>';
        
        try {
            mermaid.render('original-svg', originalCode).then(result => {
                document.getElementById('original').innerHTML += 
                    '<div style="border: 1px solid green; padding: 10px;">' + result.svg + '</div>';
            }).catch(error => {
                document.getElementById('original').innerHTML += 
                    '<div style="color: red; background: #ffe6e6; padding: 10px; border: 1px solid red;">' +
                    '❌ 渲染失败: ' + error.message + '</div>';
            });
        } catch (error) {
            console.error('Original code error:', error);
        }
        
        // 测试修复后代码
        try {
            mermaid.render('fixed-svg', fixedCode).then(result => {
                document.getElementById('fixed').innerHTML = 
                    '<h3>修复后代码:</h3><pre style="background: #e6ffe6; padding: 10px; border: 1px solid #00aa00;">' + 
                    fixedCode + '</pre>' +
                    '<div style="border: 1px solid green; padding: 10px;">' + result.svg + '</div>';
            }).catch(error => {
                document.getElementById('fixed').innerHTML = 
                    '<div style="color: red;">修复后仍然出错: ' + error.message + '</div>';
            });
        } catch (error) {
            console.error('Fixed code error:', error);
        }
    </script>
</body>
</html>