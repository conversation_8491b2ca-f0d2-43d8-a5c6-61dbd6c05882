# 开发指南

## 快速开始

### 1. 环境准备
确保你的开发环境满足以下要求：
- Node.js 18.0 或更高版本
- npm 或 yarn 包管理器
- 现代浏览器（Chrome、Firefox、Safari、Edge）

### 2. 项目设置
```bash
# 克隆项目（如果从 git 获取）
git clone <repository-url>
cd contract-review-system

# 自动设置（推荐）
chmod +x scripts/setup.sh
./scripts/setup.sh

# 或手动设置
npm install
cp env.example .env.local
```

### 3. 配置 API 密钥
编辑 `.env.local` 文件：
```ini
NEXT_PUBLIC_DIFY_ENDPOINT=https://api.dify.ai
NEXT_PUBLIC_DIFY_API_KEY=your_actual_api_key_here
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目架构

### 技术栈
- **Next.js 15**: React 框架，支持 App Router
- **React 18**: 用户界面库
- **TypeScript**: 类型安全的 JavaScript
- **Ant Design**: UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Axios**: HTTP 客户端
- **Mammoth.js**: Word 文档处理

### 文件结构
```
src/
├── app/                    # Next.js App Router
│   ├── contract-review/    # 合同审核页面
│   ├── knowledge-chat/     # 知识库问答页面
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局组件
│   └── page.tsx           # 首页组件
├── components/            # 可复用组件
│   ├── ContractUpload.tsx # 文件上传组件
│   ├── ContractViewer.tsx # 合同查看器
│   └── KnowledgeChat.tsx  # 聊天组件
├── lib/                   # 工具函数
│   ├── api.ts            # API 调用
│   └── fileUtils.ts      # 文件处理
└── types/                 # TypeScript 类型
    └── index.ts
```

## 开发规范

### 代码风格
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 使用 Prettier 格式化代码
- 组件使用 PascalCase 命名
- 文件使用 camelCase 命名

### 组件开发
1. **函数式组件**: 使用 React Hooks
2. **TypeScript**: 为 props 定义接口
3. **样式**: 优先使用 Tailwind CSS
4. **状态管理**: 使用 useState 和 useEffect

### API 集成
- 所有 API 调用在 `lib/api.ts` 中定义
- 使用 TypeScript 接口定义响应类型
- 错误处理统一在 API 层
- 使用 Axios 拦截器处理请求/响应
- **合同审核：** 使用 Workflow API，先上传文件再执行工作流
- **知识库问答：** 使用对话 API，支持多轮会话
- **API域名：** `https://ai.hongtai-idi.com/v1`

## 主要功能实现

### 合同审核流程
1. **文件上传**: ContractUpload 组件
   - 支持拖拽上传
   - 文件类型验证
   - docx 转 markdown 格式
   - 文本内容提取

2. **API 调用**: 两步式 Dify Workflow 调用
   - 第一步：上传文件到 `/files/upload`
   - 第二步：执行工作流 `/workflows/run`，传入变量名为 `hetong` 的文件

3. **结果展示**: ContractViewer 组件
   - 解析 workflow 输出结果
   - 文本高亮显示
   - 风险点批注
   - 侧边栏问题列表

### 知识库问答流程
1. **聊天界面**: KnowledgeChat 组件
   - 消息历史管理
   - 实时对话

2. **API 调用**: 调用 Dify 对话接口 `/chat-messages`
   - 发送用户问题和会话ID
   - 接收 AI 回答和新的会话ID

3. **会话管理**: 
   - conversation_id 持久化
   - 多轮对话支持
   - 阻塞模式响应

## 调试指南

### 开发工具
- **React Developer Tools**: 调试 React 组件
- **Chrome DevTools**: 网络请求和性能分析
- **TypeScript**: 编译时类型检查

### 常见问题
1. **API 调用失败**
   - 检查 `.env.local` 配置
   - 验证 API 密钥是否正确
   - 查看浏览器网络请求

2. **文件上传问题**
   - 检查文件类型是否支持
   - 验证文件大小限制
   - 查看浏览器控制台错误

3. **样式问题**
   - 检查 Tailwind CSS 配置
   - 验证 Ant Design 主题设置

### 日志调试
```typescript
// API 调用日志
console.log('API请求:', config);
console.log('API响应:', response);

// 组件状态日志
console.log('组件状态:', state);
```

## 部署

### 本地构建
```bash
npm run build
npm start
```

### Docker 部署
```bash
docker build -t contract-review-system .
docker run -p 3000:3000 contract-review-system
```

### Vercel 部署
1. 连接 GitHub 仓库
2. 配置环境变量
3. 自动部署

## 贡献指南

### 提交代码
1. 创建功能分支
2. 编写代码和测试
3. 提交 Pull Request
4. 代码审查
5. 合并到主分支

### 代码规范
- 提交前运行 `npm run lint`
- 确保 TypeScript 编译通过
- 添加必要的注释和文档

## 性能优化

### 前端优化
- 使用 Next.js 自动代码分割
- 图片懒加载
- 组件懒加载
- 缓存 API 响应

### 用户体验
- 加载状态指示器
- 错误处理和重试
- 响应式设计
- 无障碍访问

## 安全考虑

### 数据安全
- 不在前端存储敏感信息
- API 密钥通过环境变量配置
- 文件上传大小和类型限制

### API 安全
- 使用 HTTPS 传输
- API 密钥保护
- 请求超时设置

---

如有其他问题，请查看项目 README.md 或联系开发团队。