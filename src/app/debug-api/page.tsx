'use client';

import React, { useState } from 'react';
import { Card, Button, Input, Typography, Space, Alert, Divider } from 'antd';
import { reviewContract, reviewContractWithDebug } from '@/lib/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export default function DebugApiPage() {
  const [testText, setTestText] = useState(`
测试合同内容：

第一条 合同双方
甲方：测试公司A
乙方：测试公司B

第二条 合同期限
本合同有效期为一年，自2024年1月1日起至2024年12月31日止。

第三条 付款条件
乙方应在收到甲方发票后30日内付款。
  `.trim());
  
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [rawApiResponse, setRawApiResponse] = useState<any>(null);

  const handleTest = async () => {
    if (!testText.trim()) {
      setError('请输入测试文本');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('=== 开始API调试测试 ===');
      console.log('测试文本长度:', testText.length);
      console.log('测试文本内容:', testText.substring(0, 200) + '...');

      const { issues, rawResponse } = await reviewContractWithDebug(testText);
      setResult(issues);
      setRawApiResponse(rawResponse);

      console.log('=== API调试测试完成 ===');
      console.log('返回问题数量:', issues.length);
      console.log('详细结果:', issues);
      console.log('原始API响应:', rawResponse);
    } catch (err: any) {
      console.error('=== API调试测试失败 ===');
      console.error('错误对象:', err);
      console.error('错误消息:', err.message);
      console.error('错误堆栈:', err.stack);
      setError(err.message || '测试失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Title level={2}>API调试工具</Title>
        <Paragraph type="secondary">
          用于调试合同审核API的响应解析
        </Paragraph>

        <Card title="测试输入" className="mb-4">
          <Space direction="vertical" className="w-full">
            <Text strong>测试文本：</Text>
            <TextArea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              rows={8}
              placeholder="输入要测试的合同文本..."
            />
            <Button 
              type="primary" 
              onClick={handleTest}
              loading={loading}
              size="large"
            >
              {loading ? '测试中...' : '开始测试'}
            </Button>
          </Space>
        </Card>

        {error && (
          <Alert
            type="error"
            message="测试失败"
            description={error}
            className="mb-4"
          />
        )}

        {rawApiResponse && (
          <Card title="原始API响应" className="mb-4">
            <div className="bg-gray-100 p-4 rounded max-h-96 overflow-auto">
              <pre className="text-xs">
                {JSON.stringify(rawApiResponse, null, 2)}
              </pre>
            </div>
          </Card>
        )}

        {result && (
          <Card title="解析结果" className="mb-4">
            <Space direction="vertical" className="w-full">
              <Alert
                type="success"
                message={`成功解析 ${result.length} 个问题`}
                className="mb-4"
              />
              
              {result.map((issue: any, index: number) => (
                <Card 
                  key={index}
                  size="small"
                  title={`问题 ${index + 1}`}
                  className="mb-2"
                >
                  <Space direction="vertical" className="w-full">
                    <div>
                      <Text strong>原文片段：</Text>
                      <Paragraph className="ml-2 bg-gray-100 p-2 rounded">
                        {issue.原文片段 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>起始位置：</Text>
                      <Text className="ml-2">
                        {issue.起始位置 !== undefined ? issue.起始位置 : '未指定'}
                      </Text>
                    </div>
                    
                    <div>
                      <Text strong>问题描述：</Text>
                      <Paragraph className="ml-2">
                        {issue.问题描述 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>修改建议：</Text>
                      <Paragraph className="ml-2">
                        {issue.修改建议 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>问题等级：</Text>
                      <Text className="ml-2">
                        {issue.问题等级 || '未指定'}
                      </Text>
                    </div>
                    
                    {issue.段落定位 && (
                      <div>
                        <Text strong>段落定位：</Text>
                        <Text className="ml-2">
                          段落{issue.段落定位.段落序号}，偏移{issue.段落定位.段内偏移}
                        </Text>
                      </div>
                    )}
                  </Space>
                </Card>
              ))}
            </Space>
          </Card>
        )}

        <Card title="使用说明" size="small">
          <Space direction="vertical">
            <Text>1. 在上方文本框中输入测试的合同内容</Text>
            <Text>2. 点击"开始测试"按钮调用API</Text>
            <Text>3. 查看"原始API响应"了解Dify返回的确切数据</Text>
            <Text>4. 查看"解析结果"了解我们的解析是否正确</Text>
            <Text>5. 查看浏览器控制台获取详细的调试信息</Text>
          </Space>
        </Card>

        <Card title="常见问题排查" size="small" className="mt-4">
          <Space direction="vertical">
            <Text strong>如果只解析出1个问题且内容是乱码：</Text>
            <Text>• 检查原始API响应中的数据结构</Text>
            <Text>• 确认Dify返回的字段名称（可能不是"result"）</Text>
            <Text>• 检查JSON格式是否正确</Text>
            <Text strong className="mt-2">如果位置定位错误：</Text>
            <Text>• 检查"起始位置"字段是否存在</Text>
            <Text>• 确认位置索引是否为0-based</Text>
            <Text>• 检查原文片段是否为占位符</Text>
          </Space>
        </Card>
      </div>
    </div>
  );
}
