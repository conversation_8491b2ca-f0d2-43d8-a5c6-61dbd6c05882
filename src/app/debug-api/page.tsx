'use client';

import React, { useState } from 'react';
import { Card, Button, Input, Typography, Space, Alert, Divider } from 'antd';
import { reviewContract } from '@/lib/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

export default function DebugApiPage() {
  const [testText, setTestText] = useState(`
测试合同内容：

第一条 合同双方
甲方：测试公司A
乙方：测试公司B

第二条 合同期限
本合同有效期为一年，自2024年1月1日起至2024年12月31日止。

第三条 付款条件
乙方应在收到甲方发票后30日内付款。
  `.trim());
  
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleTest = async () => {
    if (!testText.trim()) {
      setError('请输入测试文本');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('开始API调试测试...');
      const issues = await reviewContract(testText);
      setResult(issues);
      console.log('API调试测试完成:', issues);
    } catch (err: any) {
      console.error('API调试测试失败:', err);
      setError(err.message || '测试失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-4xl mx-auto">
        <Title level={2}>API调试工具</Title>
        <Paragraph type="secondary">
          用于调试合同审核API的响应解析
        </Paragraph>

        <Card title="测试输入" className="mb-4">
          <Space direction="vertical" className="w-full">
            <Text strong>测试文本：</Text>
            <TextArea
              value={testText}
              onChange={(e) => setTestText(e.target.value)}
              rows={8}
              placeholder="输入要测试的合同文本..."
            />
            <Button 
              type="primary" 
              onClick={handleTest}
              loading={loading}
              size="large"
            >
              {loading ? '测试中...' : '开始测试'}
            </Button>
          </Space>
        </Card>

        {error && (
          <Alert
            type="error"
            message="测试失败"
            description={error}
            className="mb-4"
          />
        )}

        {result && (
          <Card title="解析结果" className="mb-4">
            <Space direction="vertical" className="w-full">
              <Alert
                type="success"
                message={`成功解析 ${result.length} 个问题`}
                className="mb-4"
              />
              
              {result.map((issue: any, index: number) => (
                <Card 
                  key={index}
                  size="small"
                  title={`问题 ${index + 1}`}
                  className="mb-2"
                >
                  <Space direction="vertical" className="w-full">
                    <div>
                      <Text strong>原文片段：</Text>
                      <Paragraph className="ml-2 bg-gray-100 p-2 rounded">
                        {issue.原文片段 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>起始位置：</Text>
                      <Text className="ml-2">
                        {issue.起始位置 !== undefined ? issue.起始位置 : '未指定'}
                      </Text>
                    </div>
                    
                    <div>
                      <Text strong>问题描述：</Text>
                      <Paragraph className="ml-2">
                        {issue.问题描述 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>修改建议：</Text>
                      <Paragraph className="ml-2">
                        {issue.修改建议 || '未指定'}
                      </Paragraph>
                    </div>
                    
                    <div>
                      <Text strong>问题等级：</Text>
                      <Text className="ml-2">
                        {issue.问题等级 || '未指定'}
                      </Text>
                    </div>
                    
                    {issue.段落定位 && (
                      <div>
                        <Text strong>段落定位：</Text>
                        <Text className="ml-2">
                          段落{issue.段落定位.段落序号}，偏移{issue.段落定位.段内偏移}
                        </Text>
                      </div>
                    )}
                  </Space>
                </Card>
              ))}
            </Space>
          </Card>
        )}

        <Card title="使用说明" size="small">
          <Space direction="vertical">
            <Text>1. 在上方文本框中输入测试的合同内容</Text>
            <Text>2. 点击"开始测试"按钮调用API</Text>
            <Text>3. 查看浏览器控制台获取详细的调试信息</Text>
            <Text>4. 检查解析结果是否正确</Text>
          </Space>
        </Card>
      </div>
    </div>
  );
}
