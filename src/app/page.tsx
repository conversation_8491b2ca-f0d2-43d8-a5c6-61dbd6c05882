'use client';

import React from 'react';
import { Layout, Card, Button, Typography, Space, Row, Col, Divider } from 'antd';
import { FileTextOutlined, MessageOutlined, RightOutlined, CheckCircleOutlined, SafetyOutlined, RobotOutlined } from '@ant-design/icons';
import Link from 'next/link';
import ApiHealthCheck from '@/components/ApiHealthCheck';

const { Header, Content, Footer } = Layout;
const { Title, Paragraph, Text } = Typography;

export default function HomePage() {
  return (
    <Layout className="min-h-screen">
      <Header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-full">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <RobotOutlined className="text-white text-lg" />
            </div>
            <Title level={4} className="mb-0 text-gray-800">
              智能合同审核与知识库系统
            </Title>
          </div>
          <Space>
            <Text type="secondary">AI 驱动的企业智能助手</Text>
          </Space>
        </div>
      </Header>

      <Content className="bg-gray-50">
        {/* 英雄区域 */}
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
          <div className="max-w-7xl mx-auto px-4 text-center">
            <Title className="text-4xl font-bold text-gray-800 mb-4">
              智能化合同审核与知识库问答
            </Title>
            <Paragraph className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              结合先进的AI技术，为您提供专业的合同风险识别和企业知识库智能问答服务，
              提升工作效率，降低业务风险。
            </Paragraph>
            <Space size="large">
              <Link href="/contract-review">
                <Button type="primary" size="large" icon={<FileTextOutlined />}>
                  开始合同审核
                </Button>
              </Link>
              <Link href="/knowledge-chat">
                <Button size="large" icon={<MessageOutlined />}>
                  知识库问答
                </Button>
              </Link>
            </Space>
          </div>
        </div>

        {/* API 状态检查 */}
        <div className="max-w-7xl mx-auto px-4 py-8">
          <ApiHealthCheck />
        </div>

        {/* 功能特性 */}
        <div className="max-w-7xl mx-auto px-4 py-16">
          <Title level={2} className="text-center mb-12">
            核心功能特性
          </Title>
          
          <Row gutter={[32, 32]}>
            <Col xs={24} md={12}>
              <Card 
                className="h-full hover:shadow-lg transition-shadow duration-300"
                cover={
                  <div className="bg-blue-500 text-white p-8 text-center">
                    <FileTextOutlined className="text-5xl mb-4" />
                    <Title level={3} className="text-white mb-0">
                      智能合同审核
                    </Title>
                  </div>
                }
              >
                <Space direction="vertical" size="middle" className="w-full">
                  <Paragraph className="text-gray-600">
                    利用先进的AI技术，自动识别合同中的风险条款和问题点，
                    提供专业的修改建议，帮助您规避法律风险。
                  </Paragraph>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>支持 .txt、.docx 等多种格式</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>智能高亮风险条款</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>分级风险评估（高/中/低）</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>专业修改建议</Text>
                    </div>
                  </div>

                  <Link href="/contract-review">
                    <Button type="primary" block icon={<RightOutlined />}>
                      立即体验合同审核
                    </Button>
                  </Link>
                </Space>
              </Card>
            </Col>

            <Col xs={24} md={12}>
              <Card 
                className="h-full hover:shadow-lg transition-shadow duration-300"
                cover={
                  <div className="bg-green-500 text-white p-8 text-center">
                    <MessageOutlined className="text-5xl mb-4" />
                    <Title level={3} className="text-white mb-0">
                      知识库问答
                    </Title>
                  </div>
                }
              >
                <Space direction="vertical" size="middle" className="w-full">
                  <Paragraph className="text-gray-600">
                    基于企业知识库构建的智能问答系统，快速获取企业政策、
                    流程规范、技术文档等信息。
                  </Paragraph>
                  
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>实时智能问答</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>企业文档知识库</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>上下文理解能力</Text>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircleOutlined className="text-green-500" />
                      <Text>多轮对话支持</Text>
                    </div>
                  </div>

                  <Link href="/knowledge-chat">
                    <Button type="primary" block icon={<RightOutlined />}>
                      开始知识库问答
                    </Button>
                  </Link>
                </Space>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 技术优势 */}
        <div className="bg-white py-16">
          <div className="max-w-7xl mx-auto px-4">
            <Title level={2} className="text-center mb-12">
              技术优势
            </Title>
            
            <Row gutter={[24, 24]}>
              <Col xs={24} sm={12} lg={6}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <SafetyOutlined className="text-2xl text-blue-500" />
                  </div>
                  <Title level={4}>安全可靠</Title>
                  <Text type="secondary">
                    企业级安全保障，数据传输加密，隐私信息保护
                  </Text>
                </div>
              </Col>
              
              <Col xs={24} sm={12} lg={6}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <RobotOutlined className="text-2xl text-green-500" />
                  </div>
                  <Title level={4}>AI 驱动</Title>
                  <Text type="secondary">
                    基于先进的自然语言处理技术，智能理解分析
                  </Text>
                </div>
              </Col>
              
              <Col xs={24} sm={12} lg={6}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircleOutlined className="text-2xl text-purple-500" />
                  </div>
                  <Title level={4}>高效准确</Title>
                  <Text type="secondary">
                    快速处理，准确识别，大幅提升工作效率
                  </Text>
                </div>
              </Col>
              
              <Col xs={24} sm={12} lg={6}>
                <div className="text-center">
                  <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <MessageOutlined className="text-2xl text-orange-500" />
                  </div>
                  <Title level={4}>易于使用</Title>
                  <Text type="secondary">
                    简洁直观的界面设计，无需专业技能即可上手
                  </Text>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </Content>

      <Footer className="bg-gray-800 text-white">
        <div className="max-w-7xl mx-auto px-4 py-8">
          <Row gutter={[32, 32]}>
            <Col xs={24} md={12}>
              <Title level={4} className="text-white">
                智能合同审核与知识库系统
              </Title>
              <Paragraph className="text-gray-300">
                为企业提供专业的AI驱动智能服务，提升工作效率，降低业务风险。
              </Paragraph>
            </Col>
            <Col xs={24} md={12}>
              <Title level={5} className="text-white">快速导航</Title>
              <div className="space-y-2">
                <div>
                  <Link href="/contract-review" className="text-gray-300 hover:text-white">
                    合同审核
                  </Link>
                </div>
                <div>
                  <Link href="/knowledge-chat" className="text-gray-300 hover:text-white">
                    知识库问答
                  </Link>
                </div>
              </div>
            </Col>
          </Row>
          
          <Divider className="border-gray-600 my-8" />
          
          <div className="text-center text-gray-400">
            <Text>© 2024 智能合同审核与知识库系统. 保留所有权利.</Text>
          </div>
        </div>
      </Footer>
    </Layout>
  );
}