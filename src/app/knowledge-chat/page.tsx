'use client';

import React from 'react';
import { Layout, Button, Typography, Space, Breadcrumb, Card, Row, Col, Tag } from 'antd';
import { HomeOutlined, ArrowLeftOutlined, MessageOutlined, BulbOutlined, BookOutlined, FileTextOutlined } from '@ant-design/icons';
import Link from 'next/link';
import KnowledgeChat from '@/components/KnowledgeChat';

const { Header, Content } = Layout;
const { Title, Text, Paragraph } = Typography;

export default function KnowledgeChatPage() {
  const suggestedQuestions = [
    {
      category: '企业政策',
      icon: <BookOutlined className="text-blue-500" />,
      questions: [
        '公司的请假政策是什么？',
        '员工培训计划有哪些？',
        '绩效考核标准是什么？'
      ]
    },
    {
      category: '业务流程',
      icon: <FileTextOutlined className="text-green-500" />,
      questions: [
        '采购流程的具体步骤？',
        '财务报销需要什么材料？',
        '项目审批流程是怎样的？'
      ]
    },
    {
      category: '技术文档',
      icon: <BulbOutlined className="text-purple-500" />,
      questions: [
        '系统部署文档在哪里？',
        'API 接口文档说明',
        '数据库设计规范'
      ]
    }
  ];

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-full">
          <Space align="center">
            <Link href="/">
              <Button icon={<ArrowLeftOutlined />} type="text">
                返回首页
              </Button>
            </Link>
            <Title level={4} className="mb-0">
              知识库问答
            </Title>
          </Space>
          
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />,
              },
              {
                title: '知识库问答',
              },
            ]}
          />
        </div>
      </Header>

      <Content className="p-6">
        <div className="max-w-7xl mx-auto">
          <Row gutter={[24, 24]}>
            {/* 左侧：问答建议 */}
            <Col xs={24} lg={8}>
              <Card className="h-full">
                <Title level={5} className="flex items-center gap-2 mb-4">
                  <MessageOutlined />
                  问答建议
                </Title>
                
                <Paragraph type="secondary" className="mb-6">
                  以下是一些常见问题示例，您可以参考这些问题向知识库助手提问：
                </Paragraph>

                <Space direction="vertical" size="large" className="w-full">
                  {suggestedQuestions.map((category, index) => (
                    <div key={index} className="border-l-4 border-blue-500 pl-4">
                      <div className="flex items-center gap-2 mb-3">
                        {category.icon}
                        <Text strong className="text-base">{category.category}</Text>
                      </div>
                      <div className="space-y-2">
                        {category.questions.map((question, qIndex) => (
                          <div key={qIndex} className="bg-gray-50 p-2 rounded text-sm text-gray-600 hover:bg-gray-100 cursor-pointer transition-colors">
                            • {question}
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </Space>

                <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start gap-2">
                    <BulbOutlined className="text-blue-500 mt-0.5" />
                    <div>
                      <Text strong className="text-blue-700">使用提示</Text>
                      <div className="mt-2 text-sm text-blue-600 space-y-1">
                        <div>• 尽量使用完整、具体的问题</div>
                        <div>• 可以进行多轮对话深入了解</div>
                        <div>• 支持中文自然语言交流</div>
                      </div>
                    </div>
                  </div>
                </div>
              </Card>
            </Col>

            {/* 右侧：聊天界面 */}
            <Col xs={24} lg={16}>
              <div style={{ height: 'calc(100vh - 180px)' }}>
                <KnowledgeChat className="h-full" />
              </div>
            </Col>
          </Row>

          {/* 底部功能说明 */}
          <Card className="mt-6">
            <Title level={5} className="mb-4">系统说明</Title>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={8}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <BookOutlined className="text-xl text-blue-500" />
                  </div>
                  <Text strong>知识库覆盖</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    企业政策、流程文档、技术规范等
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <MessageOutlined className="text-xl text-green-500" />
                  </div>
                  <Text strong>智能对话</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    上下文理解、多轮对话支持
                  </div>
                </div>
              </Col>
              <Col xs={24} sm={8}>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <BulbOutlined className="text-xl text-purple-500" />
                  </div>
                  <Text strong>实时更新</Text>
                  <div className="text-sm text-gray-500 mt-1">
                    知识库内容持续更新维护
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </div>
      </Content>
    </Layout>
  );
}