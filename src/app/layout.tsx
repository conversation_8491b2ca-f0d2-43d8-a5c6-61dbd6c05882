import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import ErrorBoundary from '@/components/ErrorBoundary';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: '合同审核与知识库系统',
  description: '基于AI的智能合同审核和企业知识库问答系统',
  keywords: ['合同审核', '知识库', 'AI', '智能助手'],
  authors: [{ name: '开发团队' }],
  icons: {
    icon: '/favicon.svg',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body className={inter.className}>
        <ErrorBoundary>
          <ConfigProvider
            locale={zhCN}
            theme={{
              token: {
                colorPrimary: '#1890ff',
                borderRadius: 6,
                fontSize: 14,
              },
              components: {
                Card: {
                  paddingLG: 24,
                },
                Button: {
                  borderRadius: 6,
                },
              },
            }}
          >
            {children}
          </ConfigProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}