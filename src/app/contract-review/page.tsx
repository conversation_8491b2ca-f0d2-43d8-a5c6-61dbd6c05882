'use client';

import React, { useState } from 'react';
import { Layout, Card, Button, Steps, Typography, Space, message, Breadcrumb } from 'antd';
import { HomeOutlined, FileTextOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import Link from 'next/link';
import ContractUpload from '@/components/ContractUpload';
import ContractViewer from '@/components/ContractViewer';
import { reviewContract } from '@/lib/api';
import { UploadedFile, Issue, Annotation } from '@/types';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Step } = Steps;

export default function ContractReviewPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);

  // 处理文件上传
  const handleFileUploaded = async (file: UploadedFile) => {
    setUploadedFile(file);
    setCurrentStep(1);
    setLoading(true);

    try {
      // 调用合同审核API
      console.log('开始调用合同审核API, 文件内容长度:', file.content.length);
      const issues = await reviewContract(file.content);
      console.log('API返回结果:', issues);
      
      // 转换为注释格式
      const newAnnotations: Annotation[] = issues.map((issue, index) => {
        let start = issue.起始位置;
        
        // 如果没有起始位置，尝试在文本中查找
        if (start === undefined || start === null) {
          start = file.content.indexOf(issue.原文片段);
          // 如果找不到原文片段，设置为0
          if (start === -1) {
            start = 0;
          }
        }
        
        const end = start + (issue.原文片段?.length || 50);
        
        return {
          id: index,
          start: Math.max(0, start),
          end: Math.min(file.content.length, end),
          desc: issue.问题描述 || '发现潜在问题',
          suggestion: issue.修改建议 || '建议进一步检查',
          level: issue.问题等级 || '中',
        };
      });

      console.log('转换后的注释:', newAnnotations);
      setAnnotations(newAnnotations);
      setCurrentStep(2);
      
      if (newAnnotations.length === 0) {
        message.success('恭喜！未发现明显的合同风险问题');
      } else {
        message.success(`合同审核完成，发现 ${newAnnotations.length} 个需要关注的问题`);
      }
      
    } catch (error) {
      console.error('合同审核失败:', error);
      const errorMessage = error instanceof Error ? error.message : '合同审核失败，请稍后重试';
      message.error(errorMessage);
      
      // 即使失败也显示一些基本信息
      setAnnotations([{
        id: 0,
        start: 0,
        end: 100,
        desc: '审核服务暂时不可用，这是一个演示问题',
        suggestion: '请稍后重试或联系技术支持',
        level: '中'
      }]);
      setCurrentStep(2);
    } finally {
      setLoading(false);
    }
  };

  // 重新开始
  const handleRestart = () => {
    setCurrentStep(0);
    setUploadedFile(null);
    setAnnotations([]);
    setLoading(false);
  };

  const steps = [
    {
      title: '上传合同',
      description: '选择并上传合同文件',
      icon: <FileTextOutlined />,
    },
    {
      title: 'AI 审核',
      description: '智能分析合同内容',
      icon: <FileTextOutlined />,
    },
    {
      title: '查看结果',
      description: '查看审核结果和建议',
      icon: <FileTextOutlined />,
    },
  ];

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-full">
          <Space align="center">
            <Link href="/">
              <Button icon={<ArrowLeftOutlined />} type="text">
                返回首页
              </Button>
            </Link>
            <Title level={4} className="mb-0">
              合同审核
            </Title>
          </Space>
          
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />,
              },
              {
                title: '合同审核',
              },
            ]}
          />
        </div>
      </Header>

      <Content className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* 步骤指示器 */}
          <Card className="mb-6">
            <Steps current={currentStep} items={steps} />
          </Card>

          {/* 主要内容区域 */}
          <div className="space-y-6">
            {currentStep === 0 && (
              <div className="max-w-2xl mx-auto">
                <ContractUpload 
                  onFileUploaded={handleFileUploaded}
                  loading={loading}
                />
              </div>
            )}

            {currentStep === 1 && (
              <Card className="text-center py-12">
                <Space direction="vertical" size="large">
                  <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
                  <Title level={3}>AI 正在审核您的合同</Title>
                  <Text type="secondary" className="text-lg">
                    请稍候，我们正在分析合同内容中的风险点和问题...
                  </Text>
                  {uploadedFile && (
                    <div className="bg-gray-50 p-4 rounded-lg max-w-md mx-auto">
                      <Text strong>正在处理: </Text>
                      <Text>{uploadedFile.name}</Text>
                      <br />
                      <Text type="secondary">
                        文件大小: {Math.round(uploadedFile.size / 1024)} KB | 
                        文本长度: {uploadedFile.content.length} 字符
                      </Text>
                    </div>
                  )}
                </Space>
              </Card>
            )}

            {currentStep === 2 && uploadedFile && (
              <div className="space-y-6">
                <Card>
                  <div className="flex items-center justify-between">
                    <div>
                      <Title level={4} className="mb-2">
                        审核结果
                      </Title>
                      <Text type="secondary">
                        文件: {uploadedFile.name} | 
                        发现问题: {annotations.length} 个
                      </Text>
                    </div>
                    <Button onClick={handleRestart}>
                      审核新合同
                    </Button>
                  </div>
                </Card>

                {annotations.length > 0 ? (
                  <ContractViewer
                    originalText={uploadedFile.content}
                    annotations={annotations}
                  />
                ) : (
                  <Card className="text-center py-12">
                    <Space direction="vertical" size="large">
                      <div className="text-6xl text-green-500">✓</div>
                      <Title level={3} className="text-green-600">
                        合同审核通过
                      </Title>
                      <Text type="secondary" className="text-lg">
                        恭喜！我们没有在您的合同中发现明显的风险问题。
                        合同条款看起来比较完善。
                      </Text>
                      <Button type="primary" onClick={handleRestart}>
                        审核新合同
                      </Button>
                    </Space>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </Content>
    </Layout>
  );
}