'use client';

import React, { useState } from 'react';
import { Layout, Card, Button, Steps, Typography, Space, message, Breadcrumb } from 'antd';
import { HomeOutlined, FileTextOutlined, ArrowLeftOutlined } from '@ant-design/icons';
import Link from 'next/link';
import ContractUpload from '@/components/ContractUpload';
import ContractViewer from '@/components/ContractViewer';
import { reviewContract } from '@/lib/api';
import { UploadedFile, Issue, Annotation } from '@/types';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Step } = Steps;

// 智能定位函数：根据问题描述在文本中找到相关位置
function findRelevantPosition(content: string, description: string): number {
  if (!description || !content) return 0;

  // 提取问题描述中的关键词
  const keywords = extractKeywords(description);
  console.log('提取的关键词:', keywords);

  // 尝试在文本中找到包含这些关键词的位置
  for (const keyword of keywords) {
    const position = content.indexOf(keyword);
    if (position !== -1) {
      console.log(`找到关键词 "${keyword}" 在位置 ${position}`);
      return position;
    }
  }

  // 如果没有找到关键词，返回文档开头
  return 0;
}

// 从问题描述中提取关键词
function extractKeywords(description: string): string[] {
  const keywords: string[] = [];

  // 常见的合同关键词模式
  const patterns = [
    /[""]([^""]+)[""]/, // 引号中的内容
    /【([^】]+)】/, // 中文方括号中的内容
    /\[([^\]]+)\]/, // 英文方括号中的内容
    /第[一二三四五六七八九十\d]+条/, // 条款编号
    /甲方|乙方|双方/, // 合同主体
    /付款|支付|费用|价格|金额/, // 付款相关
    /期限|时间|日期/, // 时间相关
    /违约|责任|义务/, // 责任相关
    /终止|解除|取消/, // 终止相关
  ];

  for (const pattern of patterns) {
    const matches = description.match(pattern);
    if (matches) {
      if (matches[1]) {
        keywords.push(matches[1]); // 捕获组内容
      } else {
        keywords.push(matches[0]); // 整个匹配内容
      }
    }
  }

  // 如果没有找到特殊模式，提取一些常见词汇
  if (keywords.length === 0) {
    const commonWords = description.match(/[\u4e00-\u9fa5]{2,}/g); // 中文词汇
    if (commonWords) {
      keywords.push(...commonWords.slice(0, 3)); // 取前3个词汇
    }
  }

  return keywords.filter(k => k.length > 1); // 过滤太短的词
}

export default function ContractReviewPage() {
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [annotations, setAnnotations] = useState<Annotation[]>([]);

  // 处理文件上传
  const handleFileUploaded = async (file: UploadedFile) => {
    setUploadedFile(file);
    setCurrentStep(1);
    setLoading(true);

    try {
      // 调用合同审核API
      console.log('开始调用合同审核API, 文件内容长度:', file.content.length);
      const issues = await reviewContract(file.content);
      console.log('API返回结果:', issues);
      console.log('API返回结果详细信息:');
      issues.forEach((issue, index) => {
        console.log(`问题 ${index + 1}:`, {
          原文片段: issue.原文片段,
          原文片段长度: issue.原文片段?.length,
          起始位置: issue.起始位置,
          问题描述: issue.问题描述,
          修改建议: issue.修改建议,
          问题等级: issue.问题等级
        });
      });
      
      // 转换为注释格式
      const newAnnotations: Annotation[] = issues.map((issue, index) => {
        let start = issue.起始位置;

        // 如果没有精确的起始位置，尝试多种定位方法
        if (start === undefined || start === null) {
          // 方法1: 尝试使用段落定位信息
          if (issue.段落定位) {
            try {
              const paragraphs = file.content.split('\n');
              const targetParagraph = paragraphs[issue.段落定位.段落序号 - 1]; // 转换为0-based索引
              if (targetParagraph) {
                // 计算到目标段落的字符偏移
                let paragraphStart = 0;
                for (let i = 0; i < issue.段落定位.段落序号 - 1; i++) {
                  paragraphStart += paragraphs[i].length + 1; // +1 for newline
                }
                start = paragraphStart + issue.段落定位.段内偏移;
                console.log(`使用段落定位: 段落${issue.段落定位.段落序号}, 偏移${issue.段落定位.段内偏移}, 计算位置${start}`);
              }
            } catch (error) {
              console.warn('段落定位计算失败:', error);
            }
          }

          // 方法2: 如果段落定位也失败，尝试在文本中查找原文片段
          if (start === undefined || start === null) {
            // 检查原文片段是否是有效的文本片段（不是占位符）
            const isValidFragment = issue.原文片段 &&
              issue.原文片段.length > 0 &&
              issue.原文片段 !== '整个合同' &&
              issue.原文片段 !== '未知片段' &&
              issue.原文片段 !== '全文' &&
              issue.原文片段.length < 200; // 避免过长的片段

            if (isValidFragment) {
              start = file.content.indexOf(issue.原文片段);
              console.log(`文本查找结果: "${issue.原文片段}" 位置 ${start}`);

              if (start === -1) {
                // 尝试模糊匹配（去除空格和标点）
                const cleanFragment = issue.原文片段.replace(/[\s\n\r\t，。！？；：""''（）【】]/g, '');
                const cleanContent = file.content.replace(/[\s\n\r\t，。！？；：""''（）【】]/g, '');
                const fuzzyStart = cleanContent.indexOf(cleanFragment);

                if (fuzzyStart !== -1) {
                  // 找到模糊匹配位置，需要转换回原始位置
                  let originalPos = 0;
                  let cleanPos = 0;
                  while (cleanPos < fuzzyStart && originalPos < file.content.length) {
                    if (!/[\s\n\r\t，。！？；：""''（）【】]/.test(file.content[originalPos])) {
                      cleanPos++;
                    }
                    originalPos++;
                  }
                  start = originalPos;
                  console.log(`模糊匹配成功，位置: ${start}`);
                } else {
                  start = 0;
                  console.warn(`无法定位原文片段: "${issue.原文片段}"`);
                }
              }
            } else {
              // 对于占位符或无效片段，尝试根据问题描述智能定位
              start = findRelevantPosition(file.content, issue.问题描述);
              console.log(`智能定位结果: "${issue.原文片段}" -> 位置 ${start}`);
            }
          }
        } else {
          console.log(`使用精确起始位置: ${start}`);
        }

        // 计算结束位置
        let fragmentLength = 50; // 默认长度

        if (issue.原文片段 &&
            issue.原文片段 !== '整个合同' &&
            issue.原文片段 !== '未知片段' &&
            issue.原文片段 !== '全文') {
          fragmentLength = issue.原文片段.length;
        } else {
          // 对于占位符，使用问题描述的长度作为参考，或者固定长度
          fragmentLength = Math.min(issue.问题描述?.length || 50, 100);
        }

        const end = start + fragmentLength;
        
        const annotation = {
          id: index,
          start: Math.max(0, start),
          end: Math.min(file.content.length, end),
          desc: issue.问题描述 || '发现潜在问题',
          suggestion: issue.修改建议 || '建议进一步检查',
          level: issue.问题等级 || '中',
          // 添加原始片段信息用于调试
          originalFragment: issue.原文片段,
          isPlaceholder: issue.原文片段 === '整个合同' || issue.原文片段 === '未知片段'
        };

        console.log(`注释 ${index + 1} 创建完成:`, {
          id: annotation.id,
          start: annotation.start,
          end: annotation.end,
          originalFragment: annotation.originalFragment,
          isPlaceholder: annotation.isPlaceholder
        });

        return annotation;
      });

      console.log('转换后的注释:', newAnnotations);

      // 检查是否有占位符注释
      const placeholderCount = newAnnotations.filter(ann => ann.isPlaceholder).length;
      if (placeholderCount > 0) {
        console.warn(`检测到 ${placeholderCount} 个占位符注释，可能影响精确定位`);
      }

      setAnnotations(newAnnotations);
      setCurrentStep(2);

      if (newAnnotations.length === 0) {
        message.success('恭喜！未发现明显的合同风险问题');
      } else {
        const successMsg = `合同审核完成，发现 ${newAnnotations.length} 个需要关注的问题`;
        const warningMsg = placeholderCount > 0 ?
          `（注意：${placeholderCount} 个问题可能无法精确定位到具体文本位置）` : '';
        message.success(successMsg + warningMsg);
      }
      
    } catch (error) {
      console.error('合同审核失败:', error);
      const errorMessage = error instanceof Error ? error.message : '合同审核失败，请稍后重试';
      message.error(errorMessage);
      
      // 即使失败也显示一些基本信息
      setAnnotations([{
        id: 0,
        start: 0,
        end: 100,
        desc: '审核服务暂时不可用，这是一个演示问题',
        suggestion: '请稍后重试或联系技术支持',
        level: '中'
      }]);
      setCurrentStep(2);
    } finally {
      setLoading(false);
    }
  };

  // 重新开始
  const handleRestart = () => {
    setCurrentStep(0);
    setUploadedFile(null);
    setAnnotations([]);
    setLoading(false);
  };

  const steps = [
    {
      title: '上传合同',
      description: '选择并上传合同文件',
      icon: <FileTextOutlined />,
    },
    {
      title: 'AI 审核',
      description: '智能分析合同内容',
      icon: <FileTextOutlined />,
    },
    {
      title: '查看结果',
      description: '查看审核结果和建议',
      icon: <FileTextOutlined />,
    },
  ];

  return (
    <Layout className="min-h-screen bg-gray-50">
      <Header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto flex items-center justify-between h-full">
          <Space align="center">
            <Link href="/">
              <Button icon={<ArrowLeftOutlined />} type="text">
                返回首页
              </Button>
            </Link>
            <Title level={4} className="mb-0">
              合同审核
            </Title>
          </Space>
          
          <Breadcrumb
            items={[
              {
                href: '/',
                title: <HomeOutlined />,
              },
              {
                title: '合同审核',
              },
            ]}
          />
        </div>
      </Header>

      <Content className="p-6">
        <div className="max-w-7xl mx-auto">
          {/* 步骤指示器 */}
          <Card className="mb-6">
            <Steps current={currentStep} items={steps} />
          </Card>

          {/* 主要内容区域 */}
          <div className="space-y-6">
            {currentStep === 0 && (
              <div className="max-w-2xl mx-auto">
                <ContractUpload 
                  onFileUploaded={handleFileUploaded}
                  loading={loading}
                />
              </div>
            )}

            {currentStep === 1 && (
              <Card className="text-center py-12">
                <Space direction="vertical" size="large">
                  <div className="animate-spin w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
                  <Title level={3}>AI 正在审核您的合同</Title>
                  <Text type="secondary" className="text-lg">
                    请稍候，我们正在分析合同内容中的风险点和问题...
                  </Text>
                  {uploadedFile && (
                    <div className="bg-gray-50 p-4 rounded-lg max-w-md mx-auto">
                      <Text strong>正在处理: </Text>
                      <Text>{uploadedFile.name}</Text>
                      <br />
                      <Text type="secondary">
                        文件大小: {Math.round(uploadedFile.size / 1024)} KB | 
                        文本长度: {uploadedFile.content.length} 字符
                      </Text>
                    </div>
                  )}
                </Space>
              </Card>
            )}

            {currentStep === 2 && uploadedFile && (
              <div className="space-y-6">
                <Card>
                  <div className="flex items-center justify-between">
                    <div>
                      <Title level={4} className="mb-2">
                        审核结果
                      </Title>
                      <Text type="secondary">
                        文件: {uploadedFile.name} | 
                        发现问题: {annotations.length} 个
                      </Text>
                    </div>
                    <Button onClick={handleRestart}>
                      审核新合同
                    </Button>
                  </div>
                </Card>

                {annotations.length > 0 ? (
                  <ContractViewer
                    originalText={uploadedFile.content}
                    annotations={annotations}
                  />
                ) : (
                  <Card className="text-center py-12">
                    <Space direction="vertical" size="large">
                      <div className="text-6xl text-green-500">✓</div>
                      <Title level={3} className="text-green-600">
                        合同审核通过
                      </Title>
                      <Text type="secondary" className="text-lg">
                        恭喜！我们没有在您的合同中发现明显的风险问题。
                        合同条款看起来比较完善。
                      </Text>
                      <Button type="primary" onClick={handleRestart}>
                        审核新合同
                      </Button>
                    </Space>
                  </Card>
                )}
              </div>
            )}
          </div>
        </div>
      </Content>
    </Layout>
  );
}