@tailwind base;
@tailwind components;
@tailwind utilities;

/* Markdown渲染样式 */
.markdown-content {
  line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content ul,
.markdown-content ol {
  margin-bottom: 1em;
  padding-left: 1.5em;
}

.markdown-content li {
  margin-bottom: 0.5em;
}

.markdown-content pre {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-content code {
  background-color: #f1f3f4;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.875em;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content blockquote {
  border-left: 4px solid #e2e8f0;
  padding-left: 1rem;
  margin: 1em 0;
  color: #6b7280;
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 4px;
}

.markdown-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 1em 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid #e2e8f0;
  padding: 0.5rem 1rem;
  text-align: left;
}

.markdown-content th {
  background-color: #f8f9fa;
  font-weight: 600;
}

/* Mermaid图表样式 */
.mermaid-diagram {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0;
  background-color: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
}

.mermaid-diagram svg {
  max-width: 100%;
  height: auto;
}

/* 思考过程样式 */
.thinking-content {
  background-color: #fff7ed;
  border-left: 4px solid #fb923c;
  padding: 0.75rem 1rem;
  margin: 0.5rem 0;
  border-radius: 4px;
  font-style: italic;
  color: #9a3412;
}

/* 代码高亮优化 */
.hljs {
  background: #f8f9fa !important;
  color: #24292e !important;
}

/* 聊天消息中的AI回复样式优化 */
.bg-gray-100 .markdown-content {
  color: #374151;
}

.bg-gray-100 .markdown-content h1,
.bg-gray-100 .markdown-content h2,
.bg-gray-100 .markdown-content h3,
.bg-gray-100 .markdown-content h4,
.bg-gray-100 .markdown-content h5,
.bg-gray-100 .markdown-content h6 {
  color: #1f2937;
}

.bg-gray-100 .markdown-content code {
  background-color: #e5e7eb;
}

.bg-gray-100 .markdown-content pre {
  background-color: #f3f4f6;
}

.bg-gray-100 .markdown-content blockquote {
  background-color: #f9fafb;
  border-left-color: #d1d5db;
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

/* 自定义样式 */
.highlight {
  transition: all 0.2s ease;
}

.highlight:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.contract-body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  line-height: 1.8;
}

.contract-annotation-popover .ant-popover-content {
  max-width: 400px;
}

/* 自定义滚动条 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .contract-body {
    font-size: 14px;
    line-height: 1.6;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Ant Design 组件自定义 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-btn-primary {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}