'use client';

import React, { useState } from 'react';
import { Card, Input, Button, Space, Typography, Divider } from 'antd';
import EnhancedMarkdownRenderer from '@/components/EnhancedMarkdownRenderer';

const { TextArea } = Input;
const { Title, Text } = Typography;

export default function TestMermaidPage() {
  const [content, setContent] = useState(`# Mermaid测试页面

## 基础流程图测试

\`\`\`mermaid
graph TD
    A[开始] --> B{决策}
    B -->|是| C[执行操作1]
    B -->|否| D[执行操作2]
    C --> E[结束]
    D --> E
\`\`\`

## 复杂流程图测试

\`\`\`mermaid
flowchart TD
    A[供应商申请] --> B[资质审核]
    B --> C{准入评估}
    C -->|通过| D[招标与竞争性磋商]
    C -->|不通过| B1[信息归档]
    D --> E[电子化采购流程]
    E --> F[在线发标 →加密投标 →自动评审]
    F --> G{是否中标？}
    G -->|是| H[签订战略协议]
    G -->|否| B1
    H --> I[绩效动态监控]
    
    subgraph 绩效监控体系
        I --> J[质量指标]
        I --> K[交付指标]
        I --> L[成本指标]
        I --> M[技术指标]
    end
    
    J --> N{季度考核≥90分？}
    K --> N
    L --> N
    M --> N
    N -->|是| O[升级为A类供应商]
    N -->|否| P{是否≥80分？}
    P -->|是| Q[保持B类供应商]
    P -->|否| R[启动退出程序]
    
    O --> S[深度协同创新]
    S --> T[共建联合实验室]
    S --> U[专利交叉许可]
    S --> V[紧急响应通道]
    
    R --> W[替代方案激活]
    W --> X[技术转移]
    X --> Y[2年保密协议]
    Y --> Z([流程结束])
    
    style A stroke:#2ecc71,stroke-width:3px
    style O fill:#1abc9c
    style R stroke:#e74c3c
\`\`\`

## 序列图测试

\`\`\`mermaid
sequenceDiagram
    participant 用户
    participant 系统
    participant 数据库
    
    用户->>系统: 提交申请
    系统->>数据库: 保存数据
    数据库-->>系统: 返回结果
    系统-->>用户: 显示确认
\`\`\``);

  const [customContent, setCustomContent] = useState('');

  const testCases = [
    {
      title: '简单流程图',
      content: `\`\`\`mermaid
graph TD
    A --> B
    B --> C
\`\`\``
    },
    {
      title: '无代码块标记的Mermaid（AI可能返回的格式）',
      content: `graph TD
    A[供应商申请] --> B[资质审核]
    B --> C{准入评估}
    C -->|通过| D[招标与竞争性磋商]
    C -->|不通过| E[信息归档]`
    },
    {
      title: '复杂流程图',
      content: `\`\`\`mermaid
flowchart TD
    A[开始] --> B{条件判断}
    B -->|条件1| C[操作1]
    B -->|条件2| D[操作2]
    C --> E[结束]
    D --> E
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
\`\`\``
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <Title level={1}>Mermaid渲染测试页面</Title>
        <Text type="secondary">测试和调试Mermaid图表渲染功能</Text>
        
        <div className="mt-6 grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 左侧：预设测试用例 */}
          <div>
            <Card title="预设测试用例" className="h-full">
              <Space direction="vertical" className="w-full" size="large">
                {testCases.map((testCase, index) => (
                  <div key={index}>
                    <Title level={4}>{testCase.title}</Title>
                    <Button 
                      onClick={() => setContent(testCase.content)}
                      className="mb-3"
                    >
                      加载此测试用例
                    </Button>
                    <div className="bg-gray-100 p-3 rounded text-sm">
                      <pre>{testCase.content}</pre>
                    </div>
                  </div>
                ))}
              </Space>
            </Card>
          </div>

          {/* 右侧：自定义测试 */}
          <div>
            <Card title="自定义测试" className="h-full">
              <Space direction="vertical" className="w-full" size="middle">
                <div>
                  <Text strong>输入自定义Markdown/Mermaid内容：</Text>
                  <TextArea
                    value={customContent}
                    onChange={(e) => setCustomContent(e.target.value)}
                    placeholder="输入要测试的Markdown或Mermaid代码..."
                    rows={8}
                    className="mt-2"
                  />
                  <Button 
                    type="primary"
                    onClick={() => setContent(customContent)}
                    className="mt-2"
                    disabled={!customContent.trim()}
                  >
                    测试渲染
                  </Button>
                </div>
              </Space>
            </Card>
          </div>
        </div>

        <Divider />

        {/* 渲染结果 */}
        <Card title="渲染结果" className="mt-6">
          <div className="border rounded-lg p-4 bg-white">
            <EnhancedMarkdownRenderer content={content} />
          </div>
        </Card>

        {/* 调试信息 */}
        <Card title="调试信息" className="mt-6">
          <div className="text-sm">
            <Text strong>当前内容长度：</Text> {content.length} 字符<br/>
            <Text strong>包含 Mermaid 代码块：</Text> {content.includes('```mermaid') ? '是' : '否'}<br/>
            <Text strong>包含 --> 符号：</Text> {content.includes('-->') ? '是' : '否'}<br/>
            <Text strong>包含 graph/flowchart：</Text> {/(graph|flowchart)/.test(content) ? '是' : '否'}
          </div>
          <Divider />
          <details>
            <summary className="cursor-pointer text-blue-600">查看原始内容</summary>
            <pre className="mt-2 bg-gray-100 p-3 rounded text-xs overflow-x-auto">
              {content}
            </pre>
          </details>
        </Card>
      </div>
    </div>
  );
}