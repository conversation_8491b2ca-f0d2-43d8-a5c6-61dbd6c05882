'use client';

import React, { useMemo } from 'react';
import { Card, Popover, Tag, Typography, Space, List, Button, Divider } from 'antd';
import { ExclamationCircleOutlined, InfoCircleOutlined, WarningOutlined, EyeOutlined } from '@ant-design/icons';
import { Annotation } from '@/types';
import EnhancedMarkdownRenderer from './EnhancedMarkdownRenderer';

const { Title, Text, Paragraph } = Typography;

interface ContractViewerProps {
  originalText: string;
  annotations: Annotation[];
}

// 检测文本是否包含Markdown格式
const isMarkdownContent = (text: string): boolean => {
  const markdownPatterns = [
    /^#{1,6}\s+/m,           // 标题
    /\*\*.*?\*\*/,           // 加粗
    /\*.*?\*/,               // 斜体
    /^\s*[-*+]\s+/m,         // 无序列表
    /^\s*\d+\.\s+/m,         // 有序列表
    /^\s*>\s+/m,             // 引用
    /`.*?`/,                 // 行内代码
    /```[\s\S]*?```/,        // 代码块
    /\[.*?\]\(.*?\)/,        // 链接
  ];

  return markdownPatterns.some(pattern => pattern.test(text));
};

// Markdown注释覆盖层组件
const MarkdownAnnotationOverlay: React.FC<{
  annotation: Annotation;
  originalText: string;
}> = ({ annotation, originalText }) => {
  const { id, start, end, desc, suggestion, level } = annotation;
  const config = getLevelConfig(level);

  // 对于Markdown内容，我们暂时使用简单的提示方式
  // 因为精确定位需要复杂的DOM操作
  return null; // 暂时禁用覆盖层
};

export default function ContractViewer({ originalText, annotations }: ContractViewerProps) {
  // 按起始位置排序注释，从后往前处理避免位置偏移
  const sortedAnnotations = useMemo(() => {
    return [...annotations].sort((a, b) => b.start - a.start);
  }, [annotations]);

  // 根据问题等级获取颜色和图标
  const getLevelConfig = (level?: '高' | '中' | '低') => {
    switch (level) {
      case '高':
        return {
          color: 'red',
          bgColor: 'bg-red-100 border-red-300',
          icon: <ExclamationCircleOutlined className="text-red-500" />,
          tag: <Tag color="red">高风险</Tag>
        };
      case '中':
        return {
          color: 'orange',
          bgColor: 'bg-orange-100 border-orange-300',
          icon: <WarningOutlined className="text-orange-500" />,
          tag: <Tag color="orange">中风险</Tag>
        };
      case '低':
        return {
          color: 'blue',
          bgColor: 'bg-blue-100 border-blue-300',
          icon: <InfoCircleOutlined className="text-blue-500" />,
          tag: <Tag color="blue">低风险</Tag>
        };
      default:
        return {
          color: 'gray',
          bgColor: 'bg-gray-100 border-gray-300',
          icon: <InfoCircleOutlined className="text-gray-500" />,
          tag: <Tag>待评估</Tag>
        };
    }
  };

  // 检测是否为Markdown内容
  const isMarkdown = useMemo(() => isMarkdownContent(originalText), [originalText]);

  // 渲染纯文本版本的高亮（用于Markdown内容的注释显示）
  const renderPlainTextWithHighlights = () => {
    if (!originalText || sortedAnnotations.length === 0) {
      return <span className="whitespace-pre-wrap leading-relaxed">{originalText}</span>;
    }

    let cursor = 0;
    const parts: React.ReactNode[] = [];

    // 按正序处理注释
    const forwardAnnotations = [...sortedAnnotations].reverse();

    forwardAnnotations.forEach((annotation) => {
      const { id, start, end, desc, suggestion, level } = annotation;
      const config = getLevelConfig(level);

      // 添加高亮前的普通文本
      if (cursor < start) {
        parts.push(
          <span key={`text-${cursor}`} className="whitespace-pre-wrap">
            {originalText.slice(cursor, start)}
          </span>
        );
      }

      // 添加高亮文本
      const fragment = originalText.slice(start, end);
      const popoverContent = (
        <div className="max-w-sm">
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex items-center gap-2">
              {config.icon}
              <Text strong>问题 #{id + 1}</Text>
              {config.tag}
            </div>
            <Divider className="my-2" />
            <div>
              <Text strong className="text-red-600">问题描述：</Text>
              <Paragraph className="mt-1 mb-2">{desc}</Paragraph>
            </div>
            <div>
              <Text strong className="text-green-600">修改建议：</Text>
              <Paragraph className="mt-1 mb-0">{suggestion}</Paragraph>
            </div>
          </Space>
        </div>
      );

      parts.push(
        <Popover
          key={`popover-${id}`}
          content={popoverContent}
          title={null}
          trigger={['hover', 'click']}
          placement="top"
          overlayClassName="contract-annotation-popover"
        >
          <span
            id={`annotation-${id}`}
            className={`highlight cursor-pointer transition-all duration-200 hover:shadow-md px-1 py-0.5 rounded border ${config.bgColor}`}
            style={{
              backgroundColor: config.color === 'red' ? '#fee2e2' :
                              config.color === 'orange' ? '#fed7aa' :
                              config.color === 'blue' ? '#dbeafe' : '#f3f4f6'
            }}
          >
            {fragment}
          </span>
        </Popover>
      );

      cursor = end;
    });

    // 添加剩余的文本
    if (cursor < originalText.length) {
      parts.push(
        <span key="tail" className="whitespace-pre-wrap">
          {originalText.slice(cursor)}
        </span>
      );
    }

    return <div className="leading-relaxed">{parts}</div>;
  };

  // 渲染带高亮的合同文本
  const renderHighlightedText = useMemo(() => {
    if (!originalText) {
      return null;
    }

    // 如果没有注释，直接渲染内容
    if (sortedAnnotations.length === 0) {
      if (isMarkdown) {
        return (
          <div className="markdown-content">
            <EnhancedMarkdownRenderer content={originalText} />
          </div>
        );
      } else {
        return <span className="whitespace-pre-wrap leading-relaxed">{originalText}</span>;
      }
    }

    // 对于Markdown内容，我们暂时使用混合模式：
    // 1. 显示原始Markdown渲染（保留格式）
    // 2. 在下方显示带高亮的纯文本版本（用于注释）
    if (isMarkdown) {
      console.log('使用Markdown混合渲染模式');

      return (
        <div className="markdown-content-with-annotations">
          {/* 原始Markdown渲染 */}
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-3">
              <Text strong className="text-blue-600">📄 格式化视图</Text>
              <Text type="secondary">（保留原始格式）</Text>
            </div>
            <div className="border rounded-lg p-4 bg-white">
              <EnhancedMarkdownRenderer content={originalText} />
            </div>
          </div>

          {/* 如果有注释，显示带高亮的纯文本版本 */}
          {annotations.length > 0 && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Text strong className="text-orange-600">🔍 问题标注视图</Text>
                <Text type="secondary">（显示AI识别的问题位置）</Text>
              </div>
              <div className="border rounded-lg p-4 bg-gray-50">
                {renderPlainTextWithHighlights()}
              </div>
            </div>
          )}
        </div>
      );
    }

    // 对于纯文本内容，使用原有的高亮逻辑
    let cursor = 0;
    const parts: React.ReactNode[] = [];

    // 按正序处理注释
    const forwardAnnotations = [...sortedAnnotations].reverse();

    forwardAnnotations.forEach((annotation) => {
      const { id, start, end, desc, suggestion, level } = annotation;
      const config = getLevelConfig(level);

      // 添加高亮前的普通文本
      if (cursor < start) {
        parts.push(
          <span key={`text-${cursor}`} className="whitespace-pre-wrap">
            {originalText.slice(cursor, start)}
          </span>
        );
      }

      // 添加高亮文本
      const fragment = originalText.slice(start, end);
      const popoverContent = (
        <div className="max-w-sm">
          <Space direction="vertical" size="small" className="w-full">
            <div className="flex items-center gap-2">
              {config.icon}
              <Text strong>问题 #{id + 1}</Text>
              {config.tag}
            </div>
            <Divider className="my-2" />
            <div>
              <Text strong className="text-red-600">问题描述：</Text>
              <Paragraph className="mt-1 mb-2">{desc}</Paragraph>
            </div>
            <div>
              <Text strong className="text-green-600">修改建议：</Text>
              <Paragraph className="mt-1 mb-0">{suggestion}</Paragraph>
            </div>
          </Space>
        </div>
      );

      parts.push(
        <Popover
          key={`popover-${id}`}
          content={popoverContent}
          title={null}
          trigger={['hover', 'click']}
          placement="top"
          overlayClassName="contract-annotation-popover"
        >
          <span
            id={`annotation-${id}`}
            className={`highlight cursor-pointer transition-all duration-200 hover:shadow-md px-1 py-0.5 rounded border ${config.bgColor}`}
            style={{ 
              backgroundColor: config.color === 'red' ? '#fee2e2' : 
                              config.color === 'orange' ? '#fed7aa' : 
                              config.color === 'blue' ? '#dbeafe' : '#f3f4f6'
            }}
          >
            {fragment}
          </span>
        </Popover>
      );

      cursor = end;
    });

    // 添加剩余的文本
    if (cursor < originalText.length) {
      parts.push(
        <span key="tail" className="whitespace-pre-wrap">
          {originalText.slice(cursor)}
        </span>
      );
    }

    return <div className="leading-relaxed">{parts}</div>;
  }, [originalText, sortedAnnotations]);

  // 跳转到指定注释
  const scrollToAnnotation = (id: number) => {
    const element = document.getElementById(`annotation-${id}`);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      // 添加临时高亮效果
      element.classList.add('ring-2', 'ring-yellow-400');
      setTimeout(() => {
        element.classList.remove('ring-2', 'ring-yellow-400');
      }, 2000);
    }
  };

  // 统计不同等级的问题数量
  const problemStats = useMemo(() => {
    const stats = { 高: 0, 中: 0, 低: 0, 未分级: 0 };
    annotations.forEach(annotation => {
      if (annotation.level) {
        stats[annotation.level]++;
      } else {
        stats.未分级++;
      }
    });
    return stats;
  }, [annotations]);

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
      {/* 问题列表侧边栏 */}
      <div className="lg:col-span-1">
        <Card className="sticky top-4">
          <Title level={5} className="flex items-center gap-2 mb-4">
            <EyeOutlined />
            发现的问题 ({annotations.length})
          </Title>
          
          {/* 问题统计 */}
          <div className="mb-4 p-3 bg-gray-50 rounded">
            <Space wrap>
              {problemStats.高 > 0 && <Tag color="red">高风险: {problemStats.高}</Tag>}
              {problemStats.中 > 0 && <Tag color="orange">中风险: {problemStats.中}</Tag>}
              {problemStats.低 > 0 && <Tag color="blue">低风险: {problemStats.低}</Tag>}
              {problemStats.未分级 > 0 && <Tag>未分级: {problemStats.未分级}</Tag>}
            </Space>
          </div>

          <List
            size="small"
            dataSource={annotations}
            renderItem={(annotation) => {
              const config = getLevelConfig(annotation.level);
              return (
                <List.Item
                  className="border-none px-0"
                  actions={[
                    <Button
                      key="view"
                      type="link"
                      size="small"
                      icon={<EyeOutlined />}
                      onClick={() => scrollToAnnotation(annotation.id)}
                    >
                      定位
                    </Button>
                  ]}
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {config.icon}
                      <Text strong>问题 #{annotation.id + 1}</Text>
                      {config.tag}
                    </div>
                    <Text type="secondary" className="text-xs line-clamp-2">
                      {annotation.desc}
                    </Text>
                  </div>
                </List.Item>
              );
            }}
          />
        </Card>
      </div>

      {/* 合同文本主体 */}
      <div className="lg:col-span-3">
        <Card>
          <Title level={4} className="mb-4">合同内容</Title>
          <div className="contract-body text-base leading-7 p-4 bg-white border rounded min-h-96">
            {renderHighlightedText}
          </div>
        </Card>
      </div>
    </div>
  );
}