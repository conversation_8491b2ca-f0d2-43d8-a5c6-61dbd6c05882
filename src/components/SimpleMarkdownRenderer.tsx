'use client';

import React from 'react';

interface SimpleMarkdownRendererProps {
  content: string;
  className?: string;
}

// 简单的Markdown渲染器，用于测试
const SimpleMarkdownRenderer: React.FC<SimpleMarkdownRendererProps> = ({ content, className = '' }) => {
  // 简单的Markdown解析
  const parseSimpleMarkdown = (text: string): string => {
    let html = text;
    
    // 标题
    html = html.replace(/^### (.*$)/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gm, '<h1>$1</h1>');
    
    // 加粗和斜体
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // 行内代码
    html = html.replace(/`(.*?)`/g, '<code class="inline-code">$1</code>');
    
    // 段落
    html = html.replace(/\n\n/g, '</p><p>');
    html = '<p>' + html + '</p>';
    
    // 清理空段落
    html = html.replace(/<p><\/p>/g, '');
    
    return html;
  };

  const htmlContent = parseSimpleMarkdown(content);

  return (
    <div 
      className={`simple-markdown ${className}`}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
      style={{
        lineHeight: '1.6',
        color: 'inherit'
      }}
    />
  );
};

export default SimpleMarkdownRenderer;