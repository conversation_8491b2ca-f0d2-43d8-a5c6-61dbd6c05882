'use client';

import React, { useEffect, useRef } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import mermaid from 'mermaid';

// Import highlight.js CSS
import 'highlight.js/styles/github.css';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

// Mermaid组件
const MermaidDiagram: React.FC<{ chart: string }> = ({ chart }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (ref.current && chart) {
      // 清理之前的内容
      ref.current.innerHTML = '';
      
      // 生成唯一ID
      const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      // 初始化mermaid（如果还没有初始化）
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true
        }
      });
      
      // 渲染图表
      mermaid.render(id, chart).then(({ svg }) => {
        if (ref.current) {
          ref.current.innerHTML = svg;
        }
      }).catch(error => {
        console.error('Mermaid渲染失败:', error);
        if (ref.current) {
          ref.current.innerHTML = `<pre class="bg-red-50 border border-red-200 rounded p-3 text-red-700">Mermaid图表渲染失败: ${error.message}</pre>`;
        }
      });
    }
  }, [chart]);

  return <div ref={ref} className="mermaid-diagram my-4" />;
};

// 自定义代码块组件
const CodeBlock: React.FC<any> = ({ node, inline, className, children, ...props }) => {
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';

  // 如果是mermaid图表
  if (language === 'mermaid') {
    return <MermaidDiagram chart={String(children).replace(/\n$/, '')} />;
  }

  // 普通代码块
  if (!inline && language) {
    return (
      <pre className="bg-gray-100 rounded-lg p-4 overflow-x-auto my-4">
        <code className={className} {...props}>
          {children}
        </code>
      </pre>
    );
  }

  // 行内代码
  return (
    <code className="bg-gray-100 px-2 py-1 rounded text-sm" {...props}>
      {children}
    </code>
  );
};

// 自定义表格组件
const Table: React.FC<any> = ({ children, ...props }) => (
  <div className="overflow-x-auto my-4">
    <table className="min-w-full border-collapse border border-gray-300" {...props}>
      {children}
    </table>
  </div>
);

const TableHead: React.FC<any> = ({ children, ...props }) => (
  <thead className="bg-gray-50" {...props}>
    {children}
  </thead>
);

const TableRow: React.FC<any> = ({ children, ...props }) => (
  <tr className="border-b border-gray-200" {...props}>
    {children}
  </tr>
);

const TableCell: React.FC<any> = ({ children, ...props }) => (
  <td className="border border-gray-300 px-3 py-2" {...props}>
    {children}
  </td>
);

const TableHeaderCell: React.FC<any> = ({ children, ...props }) => (
  <th className="border border-gray-300 px-3 py-2 font-semibold text-left" {...props}>
    {children}
  </th>
);

// 自定义链接组件
const Link: React.FC<any> = ({ children, href, ...props }) => (
  <a 
    href={href} 
    target="_blank" 
    rel="noopener noreferrer" 
    className="text-blue-600 hover:text-blue-800 underline"
    {...props}
  >
    {children}
  </a>
);

// 自定义标题组件
const Heading: React.FC<any> = ({ level, children, ...props }) => {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements;
  const sizeClasses = {
    1: 'text-2xl font-bold mt-6 mb-4',
    2: 'text-xl font-bold mt-5 mb-3',
    3: 'text-lg font-semibold mt-4 mb-2',
    4: 'text-base font-semibold mt-3 mb-2',
    5: 'text-sm font-semibold mt-2 mb-1',
    6: 'text-xs font-semibold mt-2 mb-1'
  };
  
  return (
    <Tag className={sizeClasses[level as keyof typeof sizeClasses] || sizeClasses[1]} {...props}>
      {children}
    </Tag>
  );
};

// 自定义段落组件
const Paragraph: React.FC<any> = ({ children, ...props }) => (
  <p className="mb-3 leading-relaxed" {...props}>
    {children}
  </p>
);

// 自定义列表组件
const List: React.FC<any> = ({ ordered, children, ...props }) => {
  if (ordered) {
    return (
      <ol className="list-decimal list-inside mb-3 space-y-1 ml-4" {...props}>
        {children}
      </ol>
    );
  }
  return (
    <ul className="list-disc list-inside mb-3 space-y-1 ml-4" {...props}>
      {children}
    </ul>
  );
};

const ListItem: React.FC<any> = ({ children, ...props }) => (
  <li className="leading-relaxed" {...props}>
    {children}
  </li>
);

// 自定义引用块组件
const Blockquote: React.FC<any> = ({ children, ...props }) => (
  <blockquote className="border-l-4 border-gray-300 pl-4 py-2 mb-3 bg-gray-50 italic" {...props}>
    {children}
  </blockquote>
);

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, className = '' }) => {
  return (
    <div className={`markdown-content ${className}`}>
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight]}
        components={{
          code: CodeBlock,
          table: Table,
          thead: TableHead,
          tr: TableRow,
          td: TableCell,
          th: TableHeaderCell,
          a: Link,
          h1: (props) => <Heading level={1} {...props} />,
          h2: (props) => <Heading level={2} {...props} />,
          h3: (props) => <Heading level={3} {...props} />,
          h4: (props) => <Heading level={4} {...props} />,
          h5: (props) => <Heading level={5} {...props} />,
          h6: (props) => <Heading level={6} {...props} />,
          p: Paragraph,
          ul: (props) => <List ordered={false} {...props} />,
          ol: (props) => <List ordered={true} {...props} />,
          li: ListItem,
          blockquote: Blockquote,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default MarkdownRenderer;