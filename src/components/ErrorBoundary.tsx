'use client';

import React from 'react';
import { Result, Button } from 'antd';
import { ExceptionOutlined } from '@ant-design/icons';

interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary 捕获到错误:', error, errorInfo);
  }

  resetError = () => {
    this.setState({ hasError: false, error: undefined });
  };

  render() {
    if (this.state.hasError) {
      // 如果有自定义的错误组件，使用自定义组件
      if (this.props.fallback) {
        const FallbackComponent = this.props.fallback;
        return <FallbackComponent error={this.state.error} resetError={this.resetError} />;
      }

      // 默认错误页面
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
          <div className="max-w-md w-full">
            <Result
              status="error"
              icon={<ExceptionOutlined className="text-red-500" />}
              title="页面出错了"
              subTitle={
                <div className="space-y-2">
                  <p>抱歉，页面遇到了一个错误。请尝试刷新页面或联系技术支持。</p>
                  {this.state.error && (
                    <details className="mt-4 text-left">
                      <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                        查看错误详情
                      </summary>
                      <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto max-h-32">
                        {this.state.error.message}
                        {'\n'}
                        {this.state.error.stack}
                      </pre>
                    </details>
                  )}
                </div>
              }
              extra={[
                <Button type="primary" key="retry" onClick={this.resetError}>
                  重试
                </Button>,
                <Button key="home" onClick={() => window.location.href = '/'}>
                  返回首页
                </Button>,
              ]}
            />
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// 函数式错误边界钩子（用于函数组件）
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    console.error('捕获到错误:', error);
    setError(error);
  }, []);

  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);

  return { handleError, resetError };
}

export default ErrorBoundary;