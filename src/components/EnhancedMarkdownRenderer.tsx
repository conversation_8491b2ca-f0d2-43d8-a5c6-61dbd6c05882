'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { fixMermaidContent, detectMermaidContent } from '@/utils/mermaidFixer';
import { debugMermaidContent } from '@/utils/debugMermaid';
import rehypeRaw from 'rehype-raw';

// 动态导入ReactMarkdown以避免SSR问题
const ReactMarkdown = dynamic(() => import('react-markdown'), {
  ssr: false,
  loading: () => <div className="animate-pulse bg-gray-200 h-4 rounded"></div>
});

interface EnhancedMarkdownRendererProps {
  content: string;
  className?: string;
  annotations?: Array<{
    id: number;
    start: number;
    end: number;
    desc: string;
    suggestion: string;
    level?: '高' | '中' | '低';
  }>;
}

// 简单的Mermaid组件
const SimpleMermaidRenderer: React.FC<{ code: string }> = ({ code }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const renderMermaid = async () => {
      try {
        // 动态导入mermaid以避免SSR问题
        const mermaid = (await import('mermaid')).default;
        
        if (!containerRef.current) return;

        // 清理之前的内容
        containerRef.current.innerHTML = '';

        // 配置mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'inherit',
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true
          },
          sequence: {
            useMaxWidth: true,
          },
          logLevel: 'debug' // 添加调试日志
        });

        // 生成唯一ID
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        // 清理和标准化代码
        let cleanCode = code.trim();
        
        // 确保以正确的图表类型开始
        if (!cleanCode.match(/^(graph|flowchart|sequenceDiagram|gantt|classDiagram|stateDiagram|journey|gitgraph|pie|requirement|mindmap|timeline)/)) {
          // 如果没有指定类型，默认为flowchart
          cleanCode = `flowchart TD\n${cleanCode}`;
        }

        console.log('Mermaid代码:', cleanCode);

        try {
          const { svg } = await mermaid.render(id, cleanCode);
          if (containerRef.current) {
            containerRef.current.innerHTML = svg;
            console.log('Mermaid渲染成功');
          }
        } catch (renderError: any) {
          console.error('Mermaid渲染错误:', renderError);
          console.error('渲染失败的代码:', cleanCode);
          setError(`渲染失败: ${renderError.message || 'Unknown error'}`);
        }
      } catch (importError) {
        console.error('Mermaid导入错误:', importError);
        setError('无法加载Mermaid库');
      }
    };

    if (code.trim()) {
      renderMermaid();
    }
  }, [code]);

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4 my-4">
        <div className="text-red-600 text-sm">
          ⚠️ 图表渲染失败: {error}
        </div>
        <details className="mt-2">
          <summary className="cursor-pointer text-red-500 text-xs">显示原始代码</summary>
          <pre className="mt-2 text-xs bg-red-100 p-2 rounded overflow-x-auto">
            <code>{code}</code>
          </pre>
        </details>
      </div>
    );
  }

  return (
    <div className="mermaid-container my-4 p-4 bg-white border border-gray-200 rounded-lg overflow-x-auto">
      <div ref={containerRef} className="flex justify-center" />
    </div>
  );
};

// 自定义代码块组件
const CodeBlock: React.FC<any> = ({ node, inline, className, children, ...props }) => {
  const match = /language-(\w+)/.exec(className || '');
  const language = match ? match[1] : '';
  const code = String(children).replace(/\n$/, '');

  // 如果是mermaid代码块
  if (language === 'mermaid' || language === 'mer') {
    return <SimpleMermaidRenderer code={code} />;
  }

  // 如果代码看起来像mermaid但没有标记语言，也尝试渲染
  if (!language && code.trim()) {
    const mermaidKeywords = ['graph', 'flowchart', 'sequenceDiagram', 'gantt', 'classDiagram', '-->', 'subgraph'];
    const looksLikeMermaid = mermaidKeywords.some(keyword => code.includes(keyword));
    if (looksLikeMermaid) {
      console.log('检测到可能的Mermaid代码:', code.substring(0, 50) + '...');
      return <SimpleMermaidRenderer code={code} />;
    }
  }

  // 行内代码
  if (inline) {
    return (
      <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono" {...props}>
        {children}
      </code>
    );
  }

  // 代码块
  return (
    <div className="my-4">
      <div className="bg-gray-800 text-gray-200 text-xs px-3 py-1 rounded-t-lg border-b border-gray-600">
        {language || 'code'}
      </div>
      <pre className="bg-gray-900 text-gray-100 rounded-b-lg p-4 overflow-x-auto">
        <code className={className} {...props}>
          {children}
        </code>
      </pre>
    </div>
  );
};

// 其他组件保持简单
const components = {
  code: CodeBlock,
  h1: ({ children, ...props }: any) => (
    <h1 className="text-2xl font-bold mt-6 mb-4 text-gray-900 border-b border-gray-200 pb-2" {...props}>
      {children}
    </h1>
  ),
  h2: ({ children, ...props }: any) => (
    <h2 className="text-xl font-bold mt-5 mb-3 text-gray-900" {...props}>
      {children}
    </h2>
  ),
  h3: ({ children, ...props }: any) => (
    <h3 className="text-lg font-semibold mt-4 mb-2 text-gray-900" {...props}>
      {children}
    </h3>
  ),
  p: ({ children, ...props }: any) => (
    <p className="mb-3 leading-relaxed text-gray-700" {...props}>
      {children}
    </p>
  ),
  ul: ({ children, ...props }: any) => (
    <ul className="list-disc list-inside mb-3 space-y-1 ml-4 text-gray-700" {...props}>
      {children}
    </ul>
  ),
  ol: ({ children, ...props }: any) => (
    <ol className="list-decimal list-inside mb-3 space-y-1 ml-4 text-gray-700" {...props}>
      {children}
    </ol>
  ),
  li: ({ children, ...props }: any) => (
    <li className="leading-relaxed" {...props}>
      {children}
    </li>
  ),
  blockquote: ({ children, ...props }: any) => (
    <blockquote className="border-l-4 border-blue-300 pl-4 py-2 mb-3 bg-blue-50 italic text-gray-600" {...props}>
      {children}
    </blockquote>
  ),
  table: ({ children, ...props }: any) => (
    <div className="overflow-x-auto my-4">
      <table className="min-w-full border-collapse border border-gray-300" {...props}>
        {children}
      </table>
    </div>
  ),
  th: ({ children, ...props }: any) => (
    <th className="border border-gray-300 px-3 py-2 bg-gray-100 font-semibold text-left" {...props}>
      {children}
    </th>
  ),
  td: ({ children, ...props }: any) => (
    <td className="border border-gray-300 px-3 py-2" {...props}>
      {children}
    </td>
  ),
  a: ({ children, href, ...props }: any) => (
    <a 
      href={href} 
      target="_blank" 
      rel="noopener noreferrer" 
      className="text-blue-600 hover:text-blue-800 underline"
      {...props}
    >
      {children}
    </a>
  ),
  strong: ({ children, ...props }: any) => (
    <strong className="font-semibold text-gray-900" {...props}>
      {children}
    </strong>
  ),
  em: ({ children, ...props }: any) => (
    <em className="italic" {...props}>
      {children}
    </em>
  ),
};

// 预处理内容，检测和修复Mermaid代码块
const preprocessContent = (content: string): string => {
  // 检测可能的Mermaid代码（没有正确的代码块标记）
  const mermaidPatterns = [
    // 检测以 graph/flowchart 开头的内容
    /^(\s*)(graph|flowchart|sequenceDiagram|gantt|classDiagram)\s+([A-Z]+.*?)$/gm,
    // 检测包含 --> 的流程图代码
    /(.*?-->.*?)/g
  ];

  let processedContent = content;

  // 查找可能的Mermaid代码段
  const lines = content.split('\n');
  let inMermaidBlock = false;
  let mermaidLines: string[] = [];
  let processedLines: string[] = [];

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    // 检测Mermaid代码开始
    if (!inMermaidBlock && (
      line.trim().match(/^(graph|flowchart|sequenceDiagram|gantt|classDiagram)/) ||
      (line.includes('-->') && line.includes('{') && line.includes('}'))
    )) {
      inMermaidBlock = true;
      mermaidLines = [line];
    } else if (inMermaidBlock) {
      mermaidLines.push(line);
      
      // 检测Mermaid代码结束（空行或明显的非Mermaid内容）
      if (line.trim() === '' && i < lines.length - 1 && !lines[i + 1].trim().match(/^[A-Z\s]+-->/)) {
        // 添加格式化的Mermaid代码块
        processedLines.push('```mermaid');
        processedLines.push(...mermaidLines.slice(0, -1)); // 排除空行
        processedLines.push('```');
        processedLines.push(''); // 添加空行
        inMermaidBlock = false;
        mermaidLines = [];
      }
    } else {
      processedLines.push(line);
    }
  }

  // 如果文档结束时还在Mermaid块中
  if (inMermaidBlock && mermaidLines.length > 0) {
    processedLines.push('```mermaid');
    processedLines.push(...mermaidLines);
    processedLines.push('```');
  }

  return processedLines.length > 0 ? processedLines.join('\n') : content;
};

const EnhancedMarkdownRenderer: React.FC<EnhancedMarkdownRendererProps> = ({ content, className = '', annotations = [] }) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // 服务端渲染时显示简单版本
  if (!isClient) {
    return (
      <div className={`markdown-content ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 rounded mb-2 w-3/4"></div>
          <div className="h-4 bg-gray-200 rounded mb-4 w-1/2"></div>
        </div>
      </div>
    );
  }

  // 预处理内容 - 使用智能Mermaid修复器
  let processedContent = fixMermaidContent(content);

  // 如果有annotations，在Markdown内容中插入高亮标记
  if (annotations && annotations.length > 0) {
    // 按起始位置倒序排列，避免位置偏移
    const sortedAnnotations = [...annotations].sort((a, b) => b.start - a.start);

    sortedAnnotations.forEach((annotation, index) => {
      const { start, end, desc, level } = annotation;
      const fragment = processedContent.slice(start, end);

      // 为不同风险等级选择不同的标记样式
      let markClass = 'annotation-highlight';
      switch (level) {
        case '高':
          markClass += ' annotation-high';
          break;
        case '中':
          markClass += ' annotation-medium';
          break;
        case '低':
          markClass += ' annotation-low';
          break;
        default:
          markClass += ' annotation-default';
      }

      // 使用HTML标记包装高亮内容，并添加title属性显示问题描述
      const highlightedFragment = `<mark class="${markClass}" title="${desc.replace(/"/g, '&quot;')}" data-annotation-id="${annotation.id}">${fragment}</mark>`;

      // 替换原始内容
      processedContent = processedContent.slice(0, start) + highlightedFragment + processedContent.slice(end);
    });
  }

  // 调试信息（仅在开发环境）
  if (process.env.NODE_ENV === 'development') {
    debugMermaidContent(content, processedContent);
  }

  // 客户端渲染
  return (
    <div className={`markdown-content ${className}`} style={{ lineHeight: '1.6' }}>
      <ReactMarkdown
        components={components}
        rehypePlugins={[
          // 允许HTML标签（用于高亮标记）
          rehypeRaw
        ]}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default EnhancedMarkdownRenderer;