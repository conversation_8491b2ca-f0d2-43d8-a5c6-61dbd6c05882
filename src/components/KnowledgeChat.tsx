'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, Input, Button, Space, Avatar, Typography, List, message, Spin, Empty, Switch } from 'antd';
import { SendOutlined, UserOutlined, RobotOutlined, MessageOutlined, ClearOutlined, BulbOutlined } from '@ant-design/icons';
import { chatKnowledgeStreaming } from '@/lib/api';
import { ChatMessage } from '@/types';
import EnhancedMarkdownRenderer from './EnhancedMarkdownRenderer';

const { TextArea } = Input;
const { Text, Title } = Typography;

interface KnowledgeChatProps {
  className?: string;
}

export default function KnowledgeChat({ className }: KnowledgeChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string>();
  const [showThinking, setShowThinking] = useState(false); // 控制是否显示thinking内容
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<any>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 发送消息 (Streaming模式)
  const handleSendMessage = async () => {
    const query = inputValue.trim();
    if (!query || loading) return;

    // 添加用户消息
    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: query,
      role: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);

    // 添加一个空的AI回复，用于streaming更新
    const assistantMessageId = (Date.now() + 1).toString();
    const initialAssistantMessage: ChatMessage = {
      id: assistantMessageId,
      content: '',
      role: 'assistant',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, initialAssistantMessage]);

    try {
      // 调用知识库API (Streaming模式)
      await chatKnowledgeStreaming(
        query, 
        sessionId,
        // onMessage回调 - 逐步更新AI回复内容
        (textChunk: string, isComplete?: boolean, isThinking?: boolean) => {
          if (!isComplete) {
            if (isThinking && showThinking) {
              // 显示thinking内容（如果开关打开）
              console.log('Thinking内容:', textChunk);
              setMessages(prev => 
                prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { 
                        ...msg, 
                        content: msg.content + `\n\n> 🤔 **思考过程**: ${textChunk}\n\n`
                      }
                    : msg
                )
              );
            } else if (!isThinking) {
              // 添加正文文本块到AI消息
              setMessages(prev => 
                prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { ...msg, content: msg.content + textChunk }
                    : msg
                )
              );
            }
          }
        },
        // onComplete回调 - 完成后更新sessionId
        (response) => {
          console.log('Streaming完成:', response);
          if (response.sessionId) {
            setSessionId(response.sessionId);
          }
        },
        showThinking // 传递是否显示thinking的设置
      );
      
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败，请稍后重试');
      
      // 更新AI消息为错误内容
      setMessages(prev => 
        prev.map(msg => 
          msg.id === assistantMessageId 
            ? { ...msg, content: '抱歉，我暂时无法回答您的问题，请稍后重试。' }
            : msg
        )
      );
    } finally {
      setLoading(false);
    }
  };

  // 清空对话
  const handleClearChat = () => {
    setMessages([]);
    setSessionId(undefined);
    message.info('对话已清空');
  };

  // 键盘事件处理
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // 格式化时间
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <Card className={`flex flex-col h-full ${className}`}>
      {/* 头部 */}
      <div className="flex items-center justify-between mb-4 pb-4 border-b">
        <Title level={4} className="mb-0 flex items-center gap-2">
          <MessageOutlined />
          知识库问答助手
        </Title>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <BulbOutlined className="text-orange-500" />
            <Text type="secondary" className="text-sm">思考过程</Text>
            <Switch 
              size="small"
              checked={showThinking}
              onChange={setShowThinking}
              title={showThinking ? "隐藏AI思考过程" : "显示AI思考过程"}
            />
          </div>
          {messages.length > 0 && (
            <Button 
              icon={<ClearOutlined />} 
              type="text" 
              size="small" 
              onClick={handleClearChat}
              className="text-gray-500"
            >
              清空对话
            </Button>
          )}
        </div>
      </div>

      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto mb-4" style={{ maxHeight: '60vh' }}>
        {messages.length === 0 ? (
          <Empty 
            description="开始与知识库助手对话吧！"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            className="py-12"
          >
            <div className="text-gray-500 text-sm">
              <p>您可以询问：</p>
              <ul className="text-left mt-2 space-y-1">
                <li>• 供应商管理流程</li>
                <li>• 采购策略和成本控制</li>
                <li>• 产品技术规范</li>
                <li>• 企业管理制度</li>
                <li>• 要求生成流程图或图表</li>
              </ul>
              <p className="mt-3 text-xs">
                💡 提示：开启"思考过程"可以看到AI的分析思路
              </p>
            </div>
          </Empty>
        ) : (
          <List
            dataSource={messages}
            renderItem={(message) => (
              <List.Item className="border-none px-0 py-2">
                <div className={`flex w-full gap-3 ${message.role === 'user' ? 'flex-row-reverse' : ''}`}>
                  <Avatar 
                    icon={message.role === 'user' ? <UserOutlined /> : <RobotOutlined />}
                    className={message.role === 'user' ? 'bg-blue-500' : 'bg-green-500'}
                  />
                  <div className={`flex-1 ${message.role === 'user' ? 'text-right' : ''}`}>
                    <div className={`inline-block max-w-[80%] p-3 rounded-lg ${
                      message.role === 'user' 
                        ? 'bg-blue-500 text-white rounded-br-none' 
                        : 'bg-gray-100 text-gray-800 rounded-bl-none'
                    }`}>
                      <div className="break-words">
                        {message.role === 'user' ? (
                          // 用户消息保持简单文本显示
                          <div className="whitespace-pre-wrap">
                            {message.content}
                          </div>
                        ) : (
                          // AI消息使用Markdown渲染
                          <>
                            {message.content ? (
                              <EnhancedMarkdownRenderer content={message.content} />
                            ) : (
                              // 如果是AI消息且正在加载且内容为空，显示typing indicator
                              loading && (
                                <div className="flex items-center gap-1">
                                  <div className="flex space-x-1">
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                                  </div>
                                </div>
                              )
                            )}
                          </>
                        )}
                      </div>
                    </div>
                    <div className={`text-xs text-gray-400 mt-1 ${message.role === 'user' ? 'text-right' : ''}`}>
                      {formatTime(message.timestamp)}
                    </div>
                  </div>
                </div>
              </List.Item>
            )}
          />
        )}
        {loading && (
          <div className="flex items-center gap-3 py-2">
            <Avatar icon={<RobotOutlined />} className="bg-green-500" />
            <div className="flex items-center gap-2 p-3 bg-gray-100 rounded-lg rounded-bl-none">
              <Spin size="small" />
              <Text type="secondary">正在思考中...</Text>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="border-t pt-4">
        <Space.Compact className="w-full">
          <TextArea
            ref={inputRef}
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="请输入您的问题...（Shift+Enter 换行，Enter 发送）"
            autoSize={{ minRows: 1, maxRows: 3 }}
            disabled={loading}
            className="flex-1"
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            loading={loading}
            disabled={!inputValue.trim()}
            className="h-auto"
          >
            发送
          </Button>
        </Space.Compact>
        
        <div className="text-xs text-gray-400 mt-2 text-center">
          基于企业知识库的智能问答，回答仅供参考
        </div>
      </div>
    </Card>
  );
}