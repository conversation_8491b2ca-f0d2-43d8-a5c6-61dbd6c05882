'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>ert, Button, Space, Typography, Spin } from 'antd';
import { CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { checkApiHealth } from '@/lib/api';

const { Text } = Typography;

interface ApiHealthCheckProps {
  className?: string;
}

export default function ApiHealthCheck({ className }: ApiHealthCheckProps) {
  const [isHealthy, setIsHealthy] = useState<boolean | null>(null);
  const [isChecking, setIsChecking] = useState(false);
  const [lastCheckTime, setLastCheckTime] = useState<Date | null>(null);

  const checkHealth = async () => {
    setIsChecking(true);
    try {
      const healthy = await checkApiHealth();
      setIsHealthy(healthy);
      setLastCheckTime(new Date());
    } catch (error) {
      console.error('健康检查失败:', error);
      setIsHealthy(false);
      setLastCheckTime(new Date());
    } finally {
      setIsChecking(false);
    }
  };

  useEffect(() => {
    // 组件加载时自动检查一次
    checkHealth();
  }, []);

  const getStatusAlert = () => {
    if (isChecking) {
      return (
        <Alert
          message="正在检查API连接状态..."
          type="info"
          icon={<Spin size="small" />}
          showIcon
        />
      );
    }

    if (isHealthy === null) {
      return null;
    }

    if (isHealthy) {
      return (
        <Alert
          message="API 连接正常"
          description="所有服务运行正常，可以开始使用系统功能"
          type="success"
          icon={<CheckCircleOutlined />}
          showIcon
        />
      );
    } else {
      return (
        <Alert
          message="API 连接异常"
          description="无法连接到服务器，请检查网络连接或联系技术支持"
          type="error"
          icon={<ExclamationCircleOutlined />}
          showIcon
        />
      );
    }
  };

  return (
    <div className={className}>
      <Space direction="vertical" size="middle" className="w-full">
        {getStatusAlert()}
        
        <div className="flex items-center justify-between">
          <div>
            {lastCheckTime && (
              <Text type="secondary" className="text-sm">
                最后检查时间: {lastCheckTime.toLocaleString()}
              </Text>
            )}
          </div>
          
          <Button
            icon={<ReloadOutlined />}
            onClick={checkHealth}
            loading={isChecking}
            size="small"
          >
            重新检查
          </Button>
        </div>
      </Space>
    </div>
  );
}