'use client';

import React, { useState } from 'react';
import { Upload, Button, message, Card, Typography, Space, Alert } from 'antd';
import { InboxOutlined, FileTextOutlined, LoadingOutlined } from '@ant-design/icons';
import type { UploadProps } from 'antd';
import { extractTextFromFile, isSupportedFileType, validateFileSize, formatFileSize, getSupportedExtensions } from '@/lib/fileUtils';
import { UploadedFile } from '@/types';

const { Dragger } = Upload;
const { Title, Text } = Typography;

interface ContractUploadProps {
  onFileUploaded: (file: UploadedFile) => void;
  loading?: boolean;
}

export default function ContractUpload({ onFileUploaded, loading = false }: ContractUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);

  const handleFileUpload = async (file: File) => {
    // 验证文件类型
    if (!isSupportedFileType(file)) {
      message.error(`不支持的文件类型。支持的格式: ${getSupportedExtensions()}`);
      return false;
    }

    // 验证文件大小
    if (!validateFileSize(file)) {
      message.error('文件大小不能超过 10MB');
      return false;
    }

    setUploading(true);

    try {
      // 提取文本内容
      const content = await extractTextFromFile(file);
      
      if (!content.trim()) {
        message.error('文件内容为空，请选择包含文本内容的文件');
        return false;
      }

      console.log('文件处理完成:');
      console.log('- 文件名:', file.name);
      console.log('- 文件类型:', file.type);
      console.log('- 内容长度:', content.length);
      console.log('- 内容预览:', content.substring(0, 200) + '...');
      console.log('- 是否包含Markdown格式:', /^#{1,6}\s+|^\s*[-*+]\s+|^\s*\d+\.\s+|\*\*.*?\*\*|\*.*?\*/m.test(content));

      const uploadedFile: UploadedFile = {
        name: file.name,
        content: content.trim(),
        type: file.type,
        size: file.size,
      };

      setUploadedFile(uploadedFile);
      onFileUploaded(uploadedFile);
      message.success('文件上传成功！');
      
    } catch (error) {
      console.error('文件处理失败:', error);
      message.error(error instanceof Error ? error.message : '文件处理失败');
    } finally {
      setUploading(false);
    }

    return false; // 阻止自动上传
  };

  const uploadProps: UploadProps = {
    name: 'file',
    multiple: false,
    accept: '.txt,.docx,.doc',
    beforeUpload: handleFileUpload,
    showUploadList: false,
    disabled: loading || uploading,
  };

  const handleRemoveFile = () => {
    setUploadedFile(null);
    message.info('已清除文件');
  };

  return (
    <Card className="w-full">
      <Space direction="vertical" size="large" className="w-full">
        <div className="text-center">
          <Title level={4}>
            <FileTextOutlined className="mr-2" />
            上传合同文件
          </Title>
          <Text type="secondary">
            支持格式: {getSupportedExtensions()} | 最大文件大小: 10MB
          </Text>
        </div>

        {!uploadedFile ? (
          <Dragger {...uploadProps} className="border-dashed border-2">
            <p className="ant-upload-drag-icon">
              {uploading ? (
                <LoadingOutlined className="text-4xl text-blue-500" />
              ) : (
                <InboxOutlined className="text-4xl text-gray-400" />
              )}
            </p>
            <p className="ant-upload-text text-lg font-medium">
              {uploading ? '正在处理文件...' : '点击或拖拽文件到此区域上传'}
            </p>
            <p className="ant-upload-hint text-gray-500">
              支持单个文件上传，请选择 .txt 或 .docx 格式的合同文件
            </p>
          </Dragger>
        ) : (
          <Alert
            message="文件上传成功"
            description={
              <Space direction="vertical" size="small" className="w-full">
                <div>
                  <strong>文件名:</strong> {uploadedFile.name}
                </div>
                <div>
                  <strong>文件大小:</strong> {formatFileSize(uploadedFile.size)}
                </div>
                <div>
                  <strong>文本长度:</strong> {uploadedFile.content.length} 字符
                </div>
                <div className="mt-2">
                  <Button type="dashed" size="small" onClick={handleRemoveFile}>
                    重新选择文件
                  </Button>
                </div>
              </Space>
            }
            type="success"
            showIcon
            className="text-left"
          />
        )}

        {loading && (
          <Alert
            message="正在分析合同..."
            description="AI 正在审核您的合同内容，请稍候"
            type="info"
            showIcon
            icon={<LoadingOutlined />}
          />
        )}
      </Space>
    </Card>
  );
}