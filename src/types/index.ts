// 段落定位信息
export interface ParagraphLocation {
  段落序号: number;         // 从1开始计数
  段内偏移: number;         // 该段中从0开始的字符位置
}

// Dify 合同审核返回项定义
export interface Issue {
  原文片段: string;         // 文本摘录
  起始位置?: number;         // 在全文中的字符索引（0-based）
  问题描述: string;         // 风险或不足之处说明
  修改建议: string;         // 建议改进措辞或条款
  问题等级?: '高' | '中' | '低';
  段落定位?: ParagraphLocation; // 容错方案：段落定位信息
}

// 前端高亮渲染项定义
export interface Annotation {
  id: number;
  start: number;
  end: number;
  desc: string;
  suggestion: string;
  level?: '高' | '中' | '低';
  originalFragment?: string;  // 原始片段文本
  isPlaceholder?: boolean;    // 是否为占位符
}

// 知识库聊天相关类型
export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export interface ChatResponse {
  answer: string;
  sessionId?: string;
  messageId?: string;
}

// 文件上传相关类型
export interface UploadedFile {
  name: string;
  content: string;
  type: string;
  size: number;
}