import mammoth from 'mammoth';

/**
 * 文件类型检查
 */
export const SUPPORTED_FILE_TYPES = {
  'text/plain': '.txt',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/msword': '.doc',
} as const;

/**
 * 检查文件类型是否支持
 * @param file 文件对象
 * @returns boolean
 */
export function isSupportedFileType(file: File): boolean {
  return Object.keys(SUPPORTED_FILE_TYPES).includes(file.type);
}

/**
 * 获取支持的文件扩展名列表
 * @returns string
 */
export function getSupportedExtensions(): string {
  return Object.values(SUPPORTED_FILE_TYPES).join(', ');
}

/**
 * 从文件中提取文本内容
 * @param file 文件对象
 * @returns Promise<string> 文本内容
 */
export async function extractTextFromFile(file: File): Promise<string> {
  if (!isSupportedFileType(file)) {
    throw new Error(`不支持的文件类型。支持的格式: ${getSupportedExtensions()}`);
  }

  try {
    switch (file.type) {
      case 'text/plain':
        return await readTextFile(file);
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'application/msword':
        return await readWordFile(file);
      
      default:
        throw new Error('未知的文件类型');
    }
  } catch (error) {
    console.error('文件读取失败:', error);
    throw new Error('文件读取失败，请检查文件格式是否正确');
  }
}

/**
 * 读取纯文本文件
 * @param file 文件对象
 * @returns Promise<string>
 */
async function readTextFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve(content || '');
    };
    reader.onerror = () => {
      reject(new Error('文本文件读取失败'));
    };
    reader.readAsText(file, 'UTF-8');
  });
}

/**
 * 读取Word文档文件并转换为markdown格式
 * @param file 文件对象
 * @returns Promise<string>
 */
async function readWordFile(file: File): Promise<string> {
  try {
    const arrayBuffer = await file.arrayBuffer();

    // 配置mammoth转换选项，保留更多格式信息
    const options = {
      arrayBuffer,
      // 自定义样式映射，保留更多格式
      styleMap: [
        "p[style-name='Heading 1'] => h1:fresh",
        "p[style-name='Heading 2'] => h2:fresh",
        "p[style-name='Heading 3'] => h3:fresh",
        "p[style-name='Heading 4'] => h4:fresh",
        "p[style-name='Heading 5'] => h5:fresh",
        "p[style-name='Heading 6'] => h6:fresh",
        "p[style-name='Title'] => h1:fresh",
        "p[style-name='Subtitle'] => h2:fresh",
        "r[style-name='Strong'] => strong",
        "r[style-name='Emphasis'] => em",
        "p[style-name='Quote'] => blockquote:fresh",
        "p[style-name='List Paragraph'] => p:fresh"
      ],
      // 转换选项
      convertImage: mammoth.images.imgElement(function(image) {
        return image.read("base64").then(function(imageBuffer) {
          return {
            src: "data:" + image.contentType + ";base64," + imageBuffer
          };
        });
      })
    };

    // 尝试转换为markdown格式
    try {
      console.log('开始转换Word文档为Markdown格式...');
      const result = await mammoth.convertToMarkdown(options);

      if (result.messages && result.messages.length > 0) {
        console.log('转换消息:', result.messages);
      }

      if (result.value && result.value.trim()) {
        console.log('Markdown转换成功，内容长度:', result.value.length);
        console.log('Markdown内容预览:', result.value.substring(0, 200) + '...');

        // 清理和优化markdown内容
        let cleanedMarkdown = result.value
          // 移除多余的空行
          .replace(/\n{3,}/g, '\n\n')
          // 确保标题前后有适当的空行
          .replace(/\n(#{1,6}\s)/g, '\n\n$1')
          .replace(/(#{1,6}\s.*)\n/g, '$1\n\n')
          // 清理首尾空白
          .trim();

        return cleanedMarkdown;
      }
    } catch (markdownError) {
      console.warn('Markdown转换失败，尝试HTML转换:', markdownError);

      // 如果markdown转换失败，尝试HTML转换然后转markdown
      try {
        const htmlResult = await mammoth.convertToHtml(options);
        if (htmlResult.value && htmlResult.value.trim()) {
          console.log('HTML转换成功，尝试简单的HTML到Markdown转换');
          // 简单的HTML到Markdown转换
          const simpleMarkdown = htmlToSimpleMarkdown(htmlResult.value);
          if (simpleMarkdown && simpleMarkdown.trim()) {
            return simpleMarkdown;
          }
        }
      } catch (htmlError) {
        console.warn('HTML转换也失败:', htmlError);
      }
    }

    // 如果所有格式转换都失败，回退到纯文本
    console.log('格式转换失败，使用纯文本模式');
    const result = await mammoth.extractRawText({ arrayBuffer });
    return result.value;
  } catch (error) {
    console.error('Word文件读取失败:', error);
    throw new Error('Word文档读取失败，请确保文件未损坏');
  }
}

/**
 * 简单的HTML到Markdown转换
 * @param html HTML字符串
 * @returns string Markdown字符串
 */
function htmlToSimpleMarkdown(html: string): string {
  return html
    // 标题转换
    .replace(/<h1[^>]*>(.*?)<\/h1>/gi, '# $1\n\n')
    .replace(/<h2[^>]*>(.*?)<\/h2>/gi, '## $1\n\n')
    .replace(/<h3[^>]*>(.*?)<\/h3>/gi, '### $1\n\n')
    .replace(/<h4[^>]*>(.*?)<\/h4>/gi, '#### $1\n\n')
    .replace(/<h5[^>]*>(.*?)<\/h5>/gi, '##### $1\n\n')
    .replace(/<h6[^>]*>(.*?)<\/h6>/gi, '###### $1\n\n')

    // 段落转换
    .replace(/<p[^>]*>(.*?)<\/p>/gi, '$1\n\n')

    // 强调和加粗
    .replace(/<strong[^>]*>(.*?)<\/strong>/gi, '**$1**')
    .replace(/<b[^>]*>(.*?)<\/b>/gi, '**$1**')
    .replace(/<em[^>]*>(.*?)<\/em>/gi, '*$1*')
    .replace(/<i[^>]*>(.*?)<\/i>/gi, '*$1*')

    // 列表转换
    .replace(/<ul[^>]*>/gi, '')
    .replace(/<\/ul>/gi, '\n')
    .replace(/<ol[^>]*>/gi, '')
    .replace(/<\/ol>/gi, '\n')
    .replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n')

    // 引用
    .replace(/<blockquote[^>]*>(.*?)<\/blockquote>/gi, '> $1\n\n')

    // 换行
    .replace(/<br[^>]*\/?>/gi, '\n')

    // 链接
    .replace(/<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi, '[$2]($1)')

    // 移除其他HTML标签
    .replace(/<[^>]*>/g, '')

    // 清理多余空行
    .replace(/\n{3,}/g, '\n\n')
    .trim();
}

/**
 * 验证文件大小（最大10MB）
 * @param file 文件对象
 * @returns boolean
 */
export function validateFileSize(file: File): boolean {
  const maxSize = 10 * 1024 * 1024; // 10MB
  return file.size <= maxSize;
}

/**
 * 格式化文件大小显示
 * @param bytes 字节数
 * @returns string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}