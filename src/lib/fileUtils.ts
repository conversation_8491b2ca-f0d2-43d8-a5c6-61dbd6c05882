import mammoth from 'mammoth';

/**
 * 文件类型检查
 */
export const SUPPORTED_FILE_TYPES = {
  'text/plain': '.txt',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
  'application/msword': '.doc',
} as const;

/**
 * 检查文件类型是否支持
 * @param file 文件对象
 * @returns boolean
 */
export function isSupportedFileType(file: File): boolean {
  return Object.keys(SUPPORTED_FILE_TYPES).includes(file.type);
}

/**
 * 获取支持的文件扩展名列表
 * @returns string
 */
export function getSupportedExtensions(): string {
  return Object.values(SUPPORTED_FILE_TYPES).join(', ');
}

/**
 * 从文件中提取文本内容
 * @param file 文件对象
 * @returns Promise<string> 文本内容
 */
export async function extractTextFromFile(file: File): Promise<string> {
  if (!isSupportedFileType(file)) {
    throw new Error(`不支持的文件类型。支持的格式: ${getSupportedExtensions()}`);
  }

  try {
    switch (file.type) {
      case 'text/plain':
        return await readTextFile(file);
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
      case 'application/msword':
        return await readWordFile(file);
      
      default:
        throw new Error('未知的文件类型');
    }
  } catch (error) {
    console.error('文件读取失败:', error);
    throw new Error('文件读取失败，请检查文件格式是否正确');
  }
}

/**
 * 读取纯文本文件
 * @param file 文件对象
 * @returns Promise<string>
 */
async function readTextFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      resolve(content || '');
    };
    reader.onerror = () => {
      reject(new Error('文本文件读取失败'));
    };
    reader.readAsText(file, 'UTF-8');
  });
}

/**
 * 读取Word文档文件并转换为markdown格式
 * @param file 文件对象
 * @returns Promise<string>
 */
async function readWordFile(file: File): Promise<string> {
  try {
    const arrayBuffer = await file.arrayBuffer();
    
    // 尝试转换为markdown格式
    try {
      const result = await mammoth.convertToMarkdown({ arrayBuffer });
      if (result.value && result.value.trim()) {
        return result.value;
      }
    } catch (markdownError) {
      console.warn('Markdown转换失败，使用纯文本模式:', markdownError);
    }
    
    // 如果markdown转换失败，回退到纯文本
    const result = await mammoth.extractRawText({ arrayBuffer });
    return result.value;
  } catch (error) {
    console.error('Word文件读取失败:', error);
    throw new Error('Word文档读取失败，请确保文件未损坏');
  }
}

/**
 * 验证文件大小（最大10MB）
 * @param file 文件对象
 * @returns boolean
 */
export function validateFileSize(file: File): boolean {
  const maxSize = 10 * 1024 * 1024; // 10MB
  return file.size <= maxSize;
}

/**
 * 格式化文件大小显示
 * @param bytes 字节数
 * @returns string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}