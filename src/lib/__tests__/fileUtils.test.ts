/**
 * 文件工具函数测试
 * 这里提供了一个基本的测试框架，实际项目中建议使用 Jest 进行完整测试
 */

import { 
  isSupportedFileType, 
  validateFileSize, 
  formatFileSize, 
  getSupportedExtensions 
} from '../fileUtils';

// 模拟测试函数
function runTests() {
  console.log('🧪 开始文件工具函数测试...');

  // 测试文件类型检查
  try {
    const txtFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const docxFile = new File(['test'], 'test.docx', { 
      type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' 
    });
    const unsupportedFile = new File(['test'], 'test.pdf', { type: 'application/pdf' });

    console.assert(isSupportedFileType(txtFile), '✅ txt 文件类型检查通过');
    console.assert(isSupportedFileType(docxFile), '✅ docx 文件类型检查通过');
    console.assert(!isSupportedFileType(unsupportedFile), '✅ 不支持的文件类型检查通过');
  } catch (error) {
    console.error('❌ 文件类型检查测试失败:', error);
  }

  // 测试文件大小验证
  try {
    const smallFile = new File(['x'.repeat(1000)], 'small.txt', { type: 'text/plain' });
    const largeFile = new File(['x'.repeat(11 * 1024 * 1024)], 'large.txt', { type: 'text/plain' });

    console.assert(validateFileSize(smallFile), '✅ 小文件大小验证通过');
    console.assert(!validateFileSize(largeFile), '✅ 大文件大小验证通过');
  } catch (error) {
    console.error('❌ 文件大小验证测试失败:', error);
  }

  // 测试文件大小格式化
  try {
    console.assert(formatFileSize(0) === '0 Bytes', '✅ 0字节格式化通过');
    console.assert(formatFileSize(1024) === '1 KB', '✅ 1KB格式化通过');
    console.assert(formatFileSize(1024 * 1024) === '1 MB', '✅ 1MB格式化通过');
  } catch (error) {
    console.error('❌ 文件大小格式化测试失败:', error);
  }

  // 测试支持的扩展名
  try {
    const extensions = getSupportedExtensions();
    console.assert(extensions.includes('.txt'), '✅ 支持的扩展名包含.txt');
    console.assert(extensions.includes('.docx'), '✅ 支持的扩展名包含.docx');
  } catch (error) {
    console.error('❌ 支持的扩展名测试失败:', error);
  }

  console.log('✅ 文件工具函数测试完成');
}

// 在浏览器环境中运行测试
if (typeof window !== 'undefined') {
  // 浏览器环境
  (window as any).runFileUtilsTests = runTests;
}

export { runTests };