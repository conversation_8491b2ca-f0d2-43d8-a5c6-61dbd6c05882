import axios, { AxiosError } from 'axios';
import { Issue, ChatResponse } from '@/types';

// API 配置
const API_BASE = 'https://ai.hongtai-idi.com/v1';
const CONTRACT_API_KEY = 'app-4S9HSpKTK7P9Ay7Llc9VUp1e';  // 合同审核智能体API Key
const KNOWLEDGE_API_KEY = 'app-vfT1is3NTxPKkYaKBRFq7ezV'; // 知识库问答智能体API Key

// 创建axios实例
const apiClient = axios.create({
  baseURL: API_BASE,
  timeout: 120000, // 120秒超时，因为workflow需要更长处理时间
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    console.log('API请求:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    console.log('API响应:', response.status, response.data);
    return response;
  },
  (error: AxiosError) => {
    console.error('API响应错误:', error.response?.status, error.response?.data);
    return Promise.reject(error);
  }
);

/**
 * 上传文件到Dify
 * @param file 文件对象
 * @param user 用户标识
 * @returns Promise<string> 文件ID
 */
async function uploadFile(file: File, user: string = 'default-user'): Promise<string> {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user', user);

  try {
    const response = await apiClient.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'Authorization': `Bearer ${CONTRACT_API_KEY}`,
      },
    });

    return response.data.id;
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败，请稍后重试');
  }
}

/**
 * 合同审核API
 * @param text 合同文本内容
 * @returns Promise<Issue[]> 审核结果
 */
export async function reviewContract(text: string): Promise<Issue[]> {
  try {
    // 直接调用workflow，传递合同文本作为字符串
    const workflowPayload = {
      inputs: {
        hetong: text  // 直接传递合同文本字符串
      },
      response_mode: 'blocking',
      user: 'default-user'
    };

    console.log('Workflow请求载荷:', JSON.stringify(workflowPayload, null, 2));
    console.log('使用的API Key:', CONTRACT_API_KEY);
    console.log('合同文本长度:', text.length);

    const response = await apiClient.post('/workflows/run', workflowPayload, {
      headers: {
        'Authorization': `Bearer ${CONTRACT_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    // 解析workflow返回结果
    const workflowResult = response.data;
    console.log('Workflow结果:', JSON.stringify(workflowResult, null, 2));
    
    if (workflowResult.data?.status === 'succeeded') {
      const outputs = workflowResult.data.outputs;
      console.log('Workflow输出:', JSON.stringify(outputs, null, 2));
      
      // 解析输出为Issue数组
      let issues: Issue[] = [];
      
      // 尝试从不同的输出字段中获取结果
      const possibleFields = ['result', 'text', 'output', 'answer'];
      let resultData = null;
      
      for (const field of possibleFields) {
        if (outputs && outputs[field]) {
          resultData = outputs[field];
          console.log(`找到输出字段: ${field}`, resultData);
          break;
        }
      }
      
      if (resultData) {
        try {
          // 尝试解析JSON字符串
          const parsedResult = typeof resultData === 'string' 
            ? JSON.parse(resultData) 
            : resultData;
          
          if (Array.isArray(parsedResult)) {
            issues = parsedResult.map((item: any, index: number) => {
              // 处理起始位置 - 支持数字或包含段落定位的对象
              let startPosition: number | undefined;

              if (typeof item.起始位置 === 'number') {
                startPosition = item.起始位置;
              } else if (typeof item.起始位置 === 'object' && item.起始位置 !== null) {
                // 如果起始位置是对象格式，尝试提取数字值
                startPosition = item.起始位置.起始位置 || item.起始位置.position;
              } else if (typeof item.start_position === 'number') {
                startPosition = item.start_position;
              }

              return {
                原文片段: item.原文片段 || item.fragment || item.text || '未知片段',
                起始位置: startPosition,
                问题描述: item.问题描述 || item.description || item.issue || '发现潜在问题',
                修改建议: item.修改建议 || item.suggestion || item.advice || '建议进一步检查',
                问题等级: item.问题等级 || item.level || item.severity || '中',
                // 保存段落定位信息（如果存在）
                段落定位: item.段落定位 || item.paragraph_location
              };
            });
          } else if (typeof parsedResult === 'object' && parsedResult !== null) {
            // 如果是单个对象，转换为数组
            issues = [{
              原文片段: parsedResult.原文片段 || parsedResult.fragment || parsedResult.text || '整个合同',
              起始位置: parsedResult.起始位置 || parsedResult.start_position,
              问题描述: parsedResult.问题描述 || parsedResult.description || parsedResult.issue || '发现潜在问题',
              修改建议: parsedResult.修改建议 || parsedResult.suggestion || parsedResult.advice || '建议进一步检查',
              问题等级: parsedResult.问题等级 || parsedResult.level || parsedResult.severity || '中'
            }];
          } else if (typeof parsedResult === 'string') {
            // 如果是字符串，创建一个基本的问题项
            issues = [{
              原文片段: '整个合同',
              问题描述: parsedResult,
              修改建议: '请根据上述分析进行相应修改',
              问题等级: '中' as const
            }];
          }
        } catch (parseError) {
          console.error('解析workflow结果失败:', parseError);
          // 如果解析失败，使用原始字符串作为描述
          issues = [{
            原文片段: '整个合同',
            问题描述: typeof resultData === 'string' ? resultData : '合同审核完成，但结果解析异常',
            修改建议: '请检查合同格式或联系技术支持',
            问题等级: '中' as const
          }];
        }
      } else {
        // 如果没有找到结果，创建一个默认消息
        issues = [{
          原文片段: '整个合同',
          问题描述: '合同审核已完成，暂未发现明显问题',
          修改建议: '建议进一步人工审核确认',
          问题等级: '低' as const
        }];
      }
      
      console.log('解析后的问题列表:', issues);
      return issues;
    } else {
      const error = workflowResult.data?.error || workflowResult.error || '未知错误';
      console.error('Workflow执行失败:', error);
      throw new Error('Workflow执行失败: ' + error);
    }
  } catch (error: any) {
    console.error('合同审核失败详细信息:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);

    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data);

      // 根据具体错误状态提供更详细的错误信息
      if (error.response.status === 400) {
        const errorData = error.response.data;
        let errorMessage = '请求参数错误';

        if (errorData && errorData.message) {
          errorMessage = `请求错误: ${errorData.message}`;
        } else if (errorData && errorData.error) {
          errorMessage = `请求错误: ${errorData.error}`;
        }

        throw new Error(errorMessage);
      } else if (error.response.status === 401) {
        throw new Error('API密钥无效，请检查配置');
      } else if (error.response.status === 404) {
        throw new Error('API接口不存在，请检查URL配置');
      }
    } else if (error.request) {
      console.error('请求对象:', error.request);
    }

    throw new Error('合同审核服务调用失败，请稍后重试');
  }
}

/**
 * 知识库问答API (Streaming模式)
 * @param query 用户问题
 * @param conversationId 会话ID（可选）
 * @param onMessage 收到消息块时的回调函数
 * @param onComplete 完成时的回调函数
 * @returns Promise<void>
 */
export async function chatKnowledgeStreaming(
  query: string, 
  conversationId?: string,
  onMessage?: (text: string, isComplete?: boolean, isThinking?: boolean) => void,
  onComplete?: (response: ChatResponse) => void,
  showThinking?: boolean
): Promise<void> {
  try {
    console.log('知识库API调用开始 (Streaming模式)');
    console.log('请求参数:', {
      query,
      conversationId,
      api_key: KNOWLEDGE_API_KEY
    });

    const requestData = {
      query,
      inputs: {},
      response_mode: 'streaming',
      user: 'default-user',
      conversation_id: conversationId || '',
      auto_generate_name: true
    };
    
    console.log('请求体:', JSON.stringify(requestData, null, 2));

    const response = await fetch(`${API_BASE}/chat-messages`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${KNOWLEDGE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('无法创建流式读取器');
    }

    const decoder = new TextDecoder();
    let buffer = '';
    let fullAnswer = '';
    let finalConversationId = '';
    let finalMessageId = '';

    try {
      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        // 将字节转换为文本并添加到缓冲区
        buffer += decoder.decode(value, { stream: true });
        
        // 处理完整的消息行（以 \n\n 分隔）
        const lines = buffer.split('\n\n');
        buffer = lines.pop() || ''; // 保留最后一个不完整的行

        for (const line of lines) {
          if (line.trim() === '') continue;
          
          // 跳过非data行（如 "event: ping"）
          if (!line.startsWith('data:')) {
            console.log('跳过非data行:', line.trim());
            continue;
          }
          
          // 移除 "data: " 前缀
          const dataLine = line.replace(/^data:\s*/, '');
          if (!dataLine || dataLine.trim() === '') continue;

          try {
            const eventData = JSON.parse(dataLine);
            console.log('Streaming事件:', eventData);

            if (eventData.event === 'message' || eventData.event === 'agent_message') {
              // 消息文本块
              let textChunk = eventData.answer || '';
              
              // 检测thinking内容
              let isThinkingContent = false;
              let isThinkingEnd = false;
              let cleanedText = textChunk;
              
              if (textChunk.includes('<details') && textChunk.includes('Thinking...')) {
                isThinkingContent = true;
                console.log('开始thinking内容:', textChunk);
                
                if (showThinking && onMessage) {
                  // 如果需要显示thinking，提取thinking内容
                  const thinkingMatch = textChunk.match(/<details[^>]*>(.*?)(?=<\/details>|$)/s);
                  if (thinkingMatch) {
                    let thinkingText = thinkingMatch[1]
                      .replace(/<summary[^>]*>.*?<\/summary>/g, '')
                      .replace(/<[^>]*>/g, '')
                      .trim();
                    if (thinkingText) {
                      onMessage(thinkingText, false, true);
                    }
                  }
                }
                
                // 移除thinking内容，保留后面的文本
                cleanedText = textChunk.replace(/<details[^>]*>[\s\S]*?(?=<\/details>|$)/, '');
              }
              
              if (textChunk.includes('</details>')) {
                isThinkingEnd = true;
                console.log('结束thinking内容:', textChunk);
                
                // 移除thinking结束标签，保留后面的文本
                cleanedText = textChunk.replace(/^[\s\S]*?<\/details>/, '');
              }
              
              // 如果整个块都是thinking内容，跳过主要处理
              if (isThinkingContent && !cleanedText.trim()) {
                continue;
              }
              
              // 移除其他HTML标签但保留Markdown格式
              cleanedText = cleanedText.replace(/<[^>]*>/g, '');
              
              // 如果处理后的文本为空或只是省略号，跳过
              if (!cleanedText || cleanedText.trim() === '……' || cleanedText.trim() === '...' || cleanedText.trim() === '') {
                continue;
              }
              
              fullAnswer += cleanedText;
              finalConversationId = eventData.conversation_id || finalConversationId;
              finalMessageId = eventData.message_id || eventData.id || finalMessageId;
              
              // 调用回调函数更新UI（传递清理后的Markdown内容）
              if (onMessage) {
                onMessage(cleanedText, false, false);
              }
            } else if (eventData.event === 'message_end') {
              // 消息结束
              finalConversationId = eventData.conversation_id || finalConversationId;
              finalMessageId = eventData.message_id || finalMessageId;
              
              console.log('Streaming完成, 完整回答:', fullAnswer);
              
              if (onMessage) {
                onMessage('', true); // 标记完成
              }
              
              if (onComplete) {
                onComplete({
                  answer: fullAnswer,
                  sessionId: finalConversationId,
                  messageId: finalMessageId
                });
              }
              return;
            } else if (eventData.event === 'error') {
              // 错误事件
              throw new Error(`流式处理错误: ${eventData.code} - ${eventData.message || '未知错误'}`);
            }
          } catch (parseError) {
            console.warn('解析流式数据失败:', parseError, '原始数据:', dataLine);
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

  } catch (error: any) {
    console.error('知识库问答失败详细信息:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    
    // 更具体的错误信息
    let errorMessage = '知识库服务调用失败，请稍后重试';
    if (error.message?.includes('401')) {
      errorMessage = 'API密钥无效，请检查配置';
    } else if (error.message?.includes('404')) {
      errorMessage = 'API接口不存在，请检查URL配置';
    } else if (error.message?.includes('500')) {
      errorMessage = '服务器内部错误，请稍后重试';
    } else if (error.name === 'TypeError' && error.message?.includes('fetch')) {
      errorMessage = '网络连接失败，请检查网络';
    }
    
    throw new Error(errorMessage);
  }
}

/**
 * 知识库问答API (阻塞模式) - 保留作为备用
 * @param query 用户问题
 * @param conversationId 会话ID（可选）
 * @returns Promise<ChatResponse> 问答结果
 */
export async function chatKnowledge(query: string, conversationId?: string): Promise<ChatResponse> {
  try {
    console.log('知识库API调用开始');
    console.log('请求参数:', {
      query,
      conversationId,
      api_key: KNOWLEDGE_API_KEY
    });

    const requestData = {
      query,
      inputs: {},
      response_mode: 'blocking',
      user: 'default-user',
      conversation_id: conversationId || '',
      auto_generate_name: true
    };
    
    console.log('请求体:', JSON.stringify(requestData, null, 2));

    const response = await apiClient.post('/chat-messages', requestData, {
      headers: {
        'Authorization': `Bearer ${KNOWLEDGE_API_KEY}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('知识库API响应状态:', response.status);
    console.log('知识库API响应数据:', JSON.stringify(response.data, null, 2));

    const result = response.data;
    
    // 检查response结构，知识库API返回的是包含event字段的对象
    let answer = '';
    let conversationId = '';
    let messageId = '';
    
    if (result.event === 'message') {
      // 这是正常的消息响应
      answer = result.answer || '抱歉，我无法回答您的问题。';
      conversationId = result.conversation_id || '';
      messageId = result.message_id || result.id || '';
    } else {
      // 兼容其他格式
      answer = result.answer || result.message || '抱歉，我无法回答您的问题。';
      conversationId = result.conversation_id || '';
      messageId = result.message_id || result.id || '';
    }
    
    return {
      answer,
      sessionId: conversationId,
      messageId
    };
  } catch (error: any) {
    console.error('知识库问答失败详细信息:');
    console.error('错误类型:', error.constructor.name);
    console.error('错误消息:', error.message);
    
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应头:', error.response.headers);
      console.error('响应数据:', error.response.data);
    } else if (error.request) {
      console.error('请求对象:', error.request);
    }
    
    // 更具体的错误信息
    let errorMessage = '知识库服务调用失败，请稍后重试';
    if (error.response?.status === 401) {
      errorMessage = 'API密钥无效，请检查配置';
    } else if (error.response?.status === 404) {
      errorMessage = 'API接口不存在，请检查URL配置';
    } else if (error.response?.status >= 500) {
      errorMessage = '服务器内部错误，请稍后重试';
    } else if (error.code === 'ECONNABORTED') {
      errorMessage = '请求超时，请检查网络连接';
    }
    
    throw new Error(errorMessage);
  }
}

/**
 * 检查API连接状态
 */
export async function checkApiHealth(): Promise<boolean> {
  try {
    // 使用知识库应用的info接口检查连接
    const response = await apiClient.get('/info', {
      headers: {
        'Authorization': `Bearer ${KNOWLEDGE_API_KEY}`,
      },
    });
    return response.status === 200;
  } catch (error) {
    console.error('API健康检查失败:', error);
    return false;
  }
}