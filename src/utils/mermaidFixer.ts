// Mermaid代码修复工具
export function fixMermaidCode(content: string): string {
  let fixedContent = content;

  // 1. 清理重复的mermaid关键字和错误格式
  fixedContent = fixedContent.replace(/mermaidmermaid/g, 'mermaid');
  fixedContent = fixedContent.replace(/```mermaid\s*mermaid/g, '```mermaid');
  fixedContent = fixedContent.replace(/mermaidflowchart/g, 'mermaid\nflowchart TD');
  fixedContent = fixedContent.replace(/mermaidgraph/g, 'mermaid\ngraph TD');
  
  // 2. 分行处理每一行
  const lines = fixedContent.split('\n');
  const cleanedLines = [];
  
  for (let line of lines) {
    line = line.trim();
    
    // 跳过空行和已经是正确格式的行
    if (!line || line.startsWith('```') || line.startsWith('flowchart') || line.startsWith('graph')) {
      if (line) cleanedLines.push(line);
      continue;
    }
    
    // 修复箭头周围的空格
    line = line.replace(/\s*-->\s*/g, ' --> ');
    
    // 修复节点定义中的空格
    line = line.replace(/([A-Za-z0-9_]+)\s*\[\s*([^\]]+?)\s*\]/g, '$1[$2]');
    line = line.replace(/([A-Za-z0-9_]+)\s*\{\s*([^}]+?)\s*\}/g, '$1{$2}');
    line = line.replace(/([A-Za-z0-9_]+)\s*\(\s*([^)]+?)\s*\)/g, '$1($2)');
    
    // 修复标签语法
    line = line.replace(/\|\s*([^|]+?)\s*\|/g, '|$1|');
    
    // 修复子图语法
    if (line.startsWith('subgraph')) {
      line = line.replace(/subgraph\s*(.+)/, 'subgraph $1');
    }
    
    // 修复样式语法
    if (line.startsWith('style ')) {
      line = line.replace(/style\s+([A-Za-z0-9_]+)\s+(.+)/, 'style $1 $2');
    }
    
    // 处理多个连续操作在一行的情况
    if (line.includes('-->') && line.split('-->').length > 2) {
      // 分解为多行
      const parts = line.split('-->');
      for (let i = 0; i < parts.length - 1; i++) {
        const from = parts[i].trim();
        const to = parts[i + 1].trim();
        if (from && to) {
          cleanedLines.push(`${from} --> ${to}`);
        }
      }
    } else {
      cleanedLines.push(line);
    }
  }

  return cleanedLines.join('\n');
}

// 检测内容是否包含Mermaid代码
export function detectMermaidContent(content: string): boolean {
  const mermaidIndicators = [
    /graph\s+(TD|TB|BT|RL|LR)/i,
    /flowchart\s+(TD|TB|BT|RL|LR)/i,
    /sequenceDiagram/i,
    /gantt/i,
    /classDiagram/i,
    /stateDiagram/i,
    /-->/,
    /subgraph\s+/i,
    /style\s+[A-Za-z0-9_]+/i
  ];

  return mermaidIndicators.some(pattern => pattern.test(content));
}

// 提取和包装Mermaid代码
export function extractAndWrapMermaid(content: string): string {
  if (!detectMermaidContent(content)) {
    return content;
  }

  const lines = content.split('\n');
  const processedLines: string[] = [];
  let mermaidLines: string[] = [];
  let inMermaidBlock = false;
  let detectedMermaidStart = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // 检测Mermaid块开始
    if (!inMermaidBlock && detectMermaidContent(line)) {
      inMermaidBlock = true;
      detectedMermaidStart = true;
      mermaidLines = [line];
      continue;
    }

    // 如果在Mermaid块中
    if (inMermaidBlock) {
      // 检测块结束条件
      if (
        line === '' && 
        i < lines.length - 1 && 
        !detectMermaidContent(lines[i + 1]) &&
        !lines[i + 1].trim().match(/^[A-Z][A-Za-z0-9_]*\s*[-->{}[\]()]/i)
      ) {
        // 结束Mermaid块
        const fixedMermaidCode = fixMermaidCode(mermaidLines.join('\n'));
        processedLines.push('```mermaid');
        processedLines.push(fixedMermaidCode);
        processedLines.push('```');
        processedLines.push('');
        inMermaidBlock = false;
        mermaidLines = [];
      } else if (line !== '') {
        mermaidLines.push(line);
      }
    } else {
      processedLines.push(lines[i]);
    }
  }

  // 如果文档结束时还在Mermaid块中
  if (inMermaidBlock && mermaidLines.length > 0) {
    const fixedMermaidCode = fixMermaidCode(mermaidLines.join('\n'));
    processedLines.push('```mermaid');
    processedLines.push(fixedMermaidCode);
    processedLines.push('```');
  }

  return detectedMermaidStart ? processedLines.join('\n') : content;
}

// 通用Mermaid内容修复器
export function fixMermaidContent(content: string): string {
  // 检查是否已经有正确的mermaid代码块标记
  if (content.includes('```mermaid')) {
    return content;
  }

  // 特殊处理：检测mermaidflowchart开头的情况
  if (content.includes('mermaidflowchart')) {
    return fixMermaidFlowchartFormat(content);
  }

  // 检测是否包含可能的Mermaid内容（通用检测）
  if (detectMermaidContent(content)) {
    return extractAndWrapMermaid(content);
  }

  return content;
}

// 修复mermaidflowchart格式问题
function fixMermaidFlowchartFormat(content: string): string {
  const lines = content.split('\n');
  let mermaidStartIndex = -1;
  let mermaidEndIndex = lines.length;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].includes('mermaidflowchart')) {
      mermaidStartIndex = i;
    }
    if (lines[i].trim() === '```' && mermaidStartIndex !== -1) {
      mermaidEndIndex = i;
      break;
    }
  }
  
  if (mermaidStartIndex !== -1) {
    const beforeMermaid = lines.slice(0, mermaidStartIndex);
    const afterMermaid = lines.slice(mermaidEndIndex + 1);
    
    // 提取并清理mermaid代码
    let mermaidLines = lines.slice(mermaidStartIndex, mermaidEndIndex);
    
    // 移除mermaidflowchart并重新格式化
    let mermaidCode = mermaidLines.join('\n')
      .replace(/^mermaidflowchart\s*/, '')
      .replace(/```\s*$/, '');
    
    // 智能检测起始节点问题
    mermaidCode = fixMissingStartNode(mermaidCode);
    
    const fixedMermaidCode = fixMermaidCode(mermaidCode);
    
    const result = [
      ...beforeMermaid,
      '```mermaid',
      'flowchart TD',
      fixedMermaidCode,
      '```',
      '',
      ...afterMermaid
    ].join('\n');

    console.log('修复mermaidflowchart格式:', result);
    return result;
  }
  
  return content;
}

// 智能修复缺失的起始节点
function fixMissingStartNode(mermaidCode: string): string {
  const lines = mermaidCode.trim().split('\n');
  if (lines.length === 0) return mermaidCode;
  
  const firstLine = lines[0].trim();
  
  // 检查是否以非A节点开始（如B、C等）
  const startMatch = firstLine.match(/^([B-Z][A-Za-z0-9_]*)\s*-->/);
  if (startMatch) {
    const missingNode = startMatch[1];
    const nodeChar = missingNode[0];
    
    // 计算应该添加的前置节点
    const prevChar = String.fromCharCode(nodeChar.charCodeAt(0) - 1);
    
    // 添加通用的起始节点
    return `${prevChar}[开始] --> ${mermaidCode}`;
  }
  
  return mermaidCode;
}

// 向后兼容的别名
export const fixSupplierFlowChart = fixMermaidContent;