// Mermaid调试工具
export function debugMermaidContent(originalContent: string, processedContent: string) {
  console.group('🔍 Mermaid调试信息');
  
  console.log('📝 原始内容长度:', originalContent.length);
  console.log('📝 处理后内容长度:', processedContent.length);
  
  console.log('🔍 原始内容前100字符:');
  console.log(originalContent.substring(0, 100) + '...');
  
  console.log('🔍 处理后内容前100字符:');
  console.log(processedContent.substring(0, 100) + '...');
  
  // 检查是否包含mermaid代码块
  const hasOriginalMermaid = originalContent.includes('```mermaid');
  const hasProcessedMermaid = processedContent.includes('```mermaid');
  
  console.log('📊 包含mermaid代码块:');
  console.log('  原始:', hasOriginalMermaid);
  console.log('  处理后:', hasProcessedMermaid);
  
  // 提取mermaid代码部分
  if (hasProcessedMermaid) {
    const mermaidMatch = processedContent.match(/```mermaid\n([\s\S]*?)\n```/);
    if (mermaidMatch) {
      const mermaidCode = mermaidMatch[1];
      console.log('🎯 提取的Mermaid代码:');
      console.log(mermaidCode);
      
      // 检查常见问题
      const issues = [];
      if (mermaidCode.includes('mermaidmermaid')) {
        issues.push('包含重复的mermaid关键字');
      }
      if (!mermaidCode.trim().match(/^(graph|flowchart|sequenceDiagram)/)) {
        issues.push('缺少图表类型声明');
      }
      if (mermaidCode.includes('-->') && !mermaidCode.match(/[A-Z][A-Za-z0-9_]*\s*-->/)) {
        issues.push('箭头语法可能有问题');
      }
      
      if (issues.length > 0) {
        console.warn('⚠️ 发现潜在问题:', issues);
      } else {
        console.log('✅ 代码格式看起来正常');
      }
    }
  }
  
  console.groupEnd();
}