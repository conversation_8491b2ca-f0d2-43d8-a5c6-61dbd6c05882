## 一、项目概述

本系统旨在为客户提供一个试用程序，集成了两大核心智能体：

1. **合同审核智能体**：通过 Dify 平台调用合同审核 API，结构化返回合同风险点，并在前端展示合同原文基础上的高亮与批注交互。
2. **企业知识库问答智能体**：基于 Dify 知识库 API 构建聊天助手，用户可就预先上传的文档内容进行问答。

前端技术栈：React 18 + Next.js 15 + TypeScript + Tailwind CSS + Ant Design。

## 二、系统架构

```
用户浏览器
  ├─ 文件上传组件 (ContractUpload)
  ├─ 智能体调用服务 (ApiService)
  ├─ 合同审核查看器 (ContractViewer)
  └─ 知识库聊天助手 (KnowledgeChat)
     │
     ↓
  Dify 平台 API
  ├─ 合同审核接口
  └─ 知识库问答接口
```

### 2.1 模块说明

- **ContractUpload**：

  - 负责接收用户上传的合同文件（支持 .txt、.docx 等格式）。
  - 读取并提取纯文本内容（通过 FileReader 或后端转换）。
  - 将合同文本传递给 ApiService。

- **ApiService**：

  - 封装 Dify 平台的 HTTP 接口调用。
  - 两个主要方法：
    - `reviewContract(text: string): Promise<Issue[]>`
    - `chatKnowledge(query: string, sessionId?: string): Promise<ChatResponse>`
  - 管理 API Key、Endpoint、超时与错误处理。

- **ContractViewer**：

  - 接收 `originalText: string` 与 `annotations: Annotation[]`。
  - 根据 `annotations`（包含 start、end、desc、suggestion、level 等）对文本进行切片与高亮。
  - 高亮片段采用 `<span>` 包裹并配合 Ant Design `Popover` 显示批注内容。
  - 支持侧边问题列表与跳转锚点。

- **KnowledgeChat**：

  - 聊天界面组件，参考 Ant Design 的 Message 和 Input 组合。
  - 用户输入问题，调用 ApiService.chatKnowledge，渲染问答对话。

## 三、数据模型

```ts
// 段落定位信息（容错方案）
interface ParagraphLocation {
  段落序号: number;         // 从1开始计数
  段内偏移: number;         // 该段中从0开始的字符位置
}

// Dify 合同审核返回项定义
interface Issue {
  原文片段: string;         // 文本摘录
  起始位置?: number;         // 在全文中的字符索引（0-based，精确定位）
  问题描述: string;         // 风险或不足之处说明
  修改建议: string;         // 建议改进措辞或条款
  问题等级?: '高' | '中' | '低';
  段落定位?: ParagraphLocation; // 容错方案：当精确索引不可用时使用
}

// 前端高亮渲染项定义
interface Annotation {
  id: number;
  start: number;
  end: number;
  desc: string;
  suggestion: string;
  level?: '高' | '中' | '低';
}
```

## 四、主要流程

1. **文件上传**
   - 用户在页面点击“上传合同”，触发 ContractUpload 组件。
   - 读取文件内容，提取纯文本 `originalText`。
2. **合同审核**
   - 调用 `ApiService.reviewContract(originalText)`，拿到 `Issue[]` 数组。
   - 前端根据 `原文片段` 或 `起始位置` 生成 `Annotation[]`：
     ```js
     const annotations = issues.map((item, idx) => ({
       id: idx,
       start: item.起始位置 ?? originalText.indexOf(item.原文片段),
       end: start + item.原文片段.length,
       desc: item.问题描述,
       suggestion: item.修改建议,
       level: item.问题等级,
     })).sort((a, b) => b.start - a.start);
     ```
3. **合同渲染**
   - ContractViewer 将 `originalText` 按 `annotations` 从后向前切片，生成 React `parts`：
     - 普通文本段落 `span`
     - 高亮段落 `Popover` + `span.highlight`
   - **位置定位策略**（多重容错）：
     1. 优先使用 `起始位置` 数字进行精确定位（0-based索引）
     2. 如果精确位置不可用，使用 `段落定位` 信息计算位置
     3. 最后回退到文本搜索 `originalText.indexOf(原文片段)`
   - 展示整体合同，并在高亮处支持鼠标悬停或点击查看批注。
   - 可选：侧边栏列表跳转，通过为高亮 `span` 添加 `id="anno-{id}"` 实现锚点链接。
4. **知识库问答**
   - KnowledgeChat 组件维护对话状态，用户提问后调用 `ApiService.chatKnowledge`。
   - 渲染历史问答记录，展示文本对话气泡。

## 五、关键代码示例

### 5.1 ApiService.ts

```ts
import axios from 'axios';

const API_BASE = process.env.NEXT_PUBLIC_DIFY_ENDPOINT;
const API_KEY = process.env.NEXT_PUBLIC_DIFY_API_KEY;

export async function reviewContract(text: string) {
  const res = await axios.post(
    `${API_BASE}/contract/review`,
    { text },
    { headers: { Authorization: `Bearer ${API_KEY}` } }
  );
  return res.data as Issue[];
}

export async function chatKnowledge(query: string, sessionId?: string) {
  const res = await axios.post(
    `${API_BASE}/knowledge/chat`,
    { query, sessionId },
    { headers: { Authorization: `Bearer ${API_KEY}` } }
  );
  return res.data as ChatResponse;
}
```

### 5.2 ContractViewer.tsx

```tsx
import { Popover, Tag } from 'antd';
import React from 'react';

export function ContractViewer({ originalText, annotations }) {
  let cursor = 0;
  const parts = [];
  annotations.forEach(({ id, start, end, desc, suggestion, level }) => {
    if (cursor < start) {
      parts.push(<span key={`t-${cursor}`}>{originalText.slice(cursor, start)}</span>);
    }
    const fragment = originalText.slice(start, end);
    parts.push(
      <Popover
        key={`p-${id}`}
        content={
          <div style={{ maxWidth: 300 }}>
            <p><strong>问题：</strong>{desc}</p>
            <p><strong>建议：</strong>{suggestion}</p>
            {level && <Tag>{level}</Tag>}
          </div>
        }
        title={`问题 #${id + 1}`}
        trigger="hover"
      >
        <span id={`anno-${id}`} className="highlight">{fragment}</span>
      </Popover>
    );
    cursor = end;
  });
  if (cursor < originalText.length) {
    parts.push(<span key="tail">{originalText.slice(cursor)}</span>);
  }
  return <div className="contract-body">{parts}</div>;
}
```

## 六、页面布局与样式

- **合同审核页** `/pages/contract-review.tsx`

  - ContractUpload
  - 加载中指示器
  - ContractViewer
  - 问题列表侧边栏（可选）

- **知识库问答页** `/pages/knowledge-chat.tsx`

  - ChatMessageList
  - ChatInput

- **全局样式**（使用 Tailwind）

  - `.highlight { @apply bg-red-200 cursor-pointer; }`
  - `.contract-body { @apply whitespace-pre-wrap leading-relaxed; }`

## 七、环境与部署

1. **环境变量**（.env.local）
   ```ini
   NEXT_PUBLIC_DIFY_ENDPOINT=https://api.dify.ai
   NEXT_PUBLIC_DIFY_API_KEY=your_api_key_here
   ```
2. **本地运行**
   ```bash
   npm install
   npm run dev
   ```
3. **生产环境构建**
   ```bash
   npm run build
   npm start
   ```
4. **Docker 部署**
   ```dockerfile
   FROM node:18-alpine
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci --production
   COPY . .
   RUN npm run build
   CMD ["npm","start"]
   ```

---

以上即为前端试用系统的详细开发说明，涵盖模块划分、数据流、关键代码示例及部署指引。可根据实际需求进一步补充权限、日志、测试等功能模块。

