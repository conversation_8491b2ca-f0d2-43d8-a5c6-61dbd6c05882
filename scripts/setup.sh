#!/bin/bash

echo "🚀 开始设置合同审核与知识库系统..."

# 检查 Node.js 版本
if command -v node > /dev/null 2>&1; then
    NODE_VERSION=$(node --version)
    echo "✅ 检测到 Node.js $NODE_VERSION"
else
    echo "❌ 未找到 Node.js，请先安装 Node.js 18 或更高版本"
    echo "访问 https://nodejs.org/ 下载安装"
    exit 1
fi

# 检查 npm 版本
if command -v npm > /dev/null 2>&1; then
    NPM_VERSION=$(npm --version)
    echo "✅ 检测到 npm $NPM_VERSION"
else
    echo "❌ 未找到 npm"
    exit 1
fi

# 安装依赖
echo "📦 安装项目依赖..."
npm install

if [ $? -eq 0 ]; then
    echo "✅ 依赖安装成功"
else
    echo "❌ 依赖安装失败"
    exit 1
fi

# 检查环境配置文件
if [ ! -f ".env.local" ]; then
    if [ -f "env.example" ]; then
        echo "📝 创建环境配置文件..."
        cp env.example .env.local
        echo "✅ 已创建 .env.local 文件"
        echo "⚠️  请编辑 .env.local 文件，配置您的 Dify API 密钥"
    else
        echo "⚠️  请手动创建 .env.local 文件并配置 API 密钥"
    fi
else
    echo "✅ 环境配置文件已存在"
fi

echo ""
echo "🎉 系统设置完成！"
echo ""
echo "📋 下一步操作："
echo "1. 编辑 .env.local 文件，配置您的 Dify API 密钥"
echo "2. 运行 'npm run dev' 启动开发服务器"
echo "3. 在浏览器中访问 http://localhost:3000"
echo ""
echo "📚 更多信息请查看 README.md 文件"