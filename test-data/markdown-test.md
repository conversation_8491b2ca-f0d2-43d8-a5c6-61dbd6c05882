# Markdown测试文档

## 基本格式测试

这是一个**粗体**文本和*斜体*文本的示例。

### 列表测试

- 项目1
- 项目2  
  - 子项目2.1
  - 子项目2.2
- 项目3

1. 有序列表1
2. 有序列表2
3. 有序列表3

### 代码测试

行内代码：`console.log('Hello World')`

代码块：
```javascript
function greet(name) {
    return `Hello, ${name}!`;
}

console.log(greet('World'));
```

### 引用测试

> 这是一个引用块示例
> 可以包含多行内容

### 表格测试

| 产品名称 | 价格 | 库存 |
|---------|------|------|
| 产品A   | ¥100 | 50   |
| 产品B   | ¥200 | 30   |
| 产品C   | ¥150 | 20   |

### Mermaid图表测试

```mermaid
graph TD
    A[开始] --> B{决策}
    B -->|是| C[执行操作1]
    B -->|否| D[执行操作2]
    C --> E[结束]
    D --> E
```

### 流程图示例

```mermaid
flowchart LR
    A[供应商注册] --> B[资质审核]
    B --> C{审核通过?}
    C -->|是| D[加入供应商库]
    C -->|否| E[重新提交]
    E --> B
    D --> F[开始采购合作]
```

### 序列图示例

```mermaid
sequenceDiagram
    participant 用户
    participant 系统
    participant 数据库
    
    用户->>系统: 提交申请
    系统->>数据库: 保存数据
    数据库-->>系统: 返回结果
    系统-->>用户: 显示确认
```

## 结论

这个文档包含了各种Markdown元素和Mermaid图表，用于测试渲染功能。