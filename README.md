# 合同审核与知识库前端集成系统

一个基于 Next.js 和 Ant Design 构建的智能合同审核与企业知识库问答系统。

## 🚀 功能特性

### 智能合同审核
- 📄 支持多种文件格式（.txt、.docx）
- 🎨 **Markdown格式保留** - Word文档转换为Markdown，保留原有格式
- 🔍 AI 驱动的风险点识别
- 🎯 智能文本高亮和批注
- 📊 分级风险评估（高/中/低）
- 💡 专业修改建议

### 知识库问答
- 💬 实时智能问答
- 🧠 上下文理解能力
- 📚 企业文档知识库
- 🔄 多轮对话支持
- 🎨 直观的聊天界面

## 🛠️ 技术栈

- **前端框架**: Next.js 15 + React 18
- **UI 组件**: Ant Design
- **样式方案**: Tailwind CSS
- **语言**: TypeScript
- **API 集成**: Dify 平台
- **文件处理**: Mammoth.js (Word转Markdown)
- **Markdown渲染**: React-Markdown + 语法高亮

## 📦 安装与运行

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 环境配置
复制并配置环境变量：
```bash
cp env.example .env.local
```

编辑 `.env.local` 文件，配置 Dify API（可选，API Key已内置）：
```ini
# Dify API 配置
# API基础地址（固定值）
NEXT_PUBLIC_DIFY_ENDPOINT=https://ai.hongtai-idi.com/v1

# 合同审核智能体API Key
NEXT_PUBLIC_CONTRACT_API_KEY=app-4S9HSpKTK7P9Ay7Llc9VUp1e

# 知识库问答智能体API Key
NEXT_PUBLIC_KNOWLEDGE_API_KEY=app-vfT1is3NTxPKkYaKBRFq7ezV
```

**注意：** API Key 已经内置在代码中，如无特殊需求无需修改。

### 开发运行
```bash
npm run dev
# 或
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 生产构建
```bash
npm run build
npm start
# 或
yarn build
yarn start
```

## 📁 项目结构

```
src/
├── app/                      # Next.js App Router
│   ├── contract-review/      # 合同审核页面
│   ├── knowledge-chat/       # 知识库问答页面
│   ├── globals.css          # 全局样式
│   ├── layout.tsx           # 根布局
│   └── page.tsx             # 首页
├── components/              # React 组件
│   ├── ContractUpload.tsx   # 合同上传组件
│   ├── ContractViewer.tsx   # 合同查看器
│   └── KnowledgeChat.tsx    # 知识库聊天组件
├── lib/                     # 工具库
│   ├── api.ts              # API 服务
│   └── fileUtils.ts        # 文件处理工具
└── types/                   # TypeScript 类型定义
    └── index.ts
```

## 🔧 核心组件

### ContractUpload
文件上传组件，支持：
- 拖拽上传
- 文件类型验证
- 文件大小限制
- 文本内容提取

### ContractViewer
合同查看器，提供：
- 智能文本高亮
- 风险点批注
- 问题列表导航
- 多级风险标识

### KnowledgeChat
知识库聊天组件，具备：
- 实时对话界面
- 消息历史管理
- 加载状态显示
- 会话持久化

## 🎨 样式特性

- 🎨 现代化的 UI 设计
- 📱 完全响应式布局
- 🌈 丰富的交互动画
- 🎯 直观的用户体验
- 🔍 高对比度的文本高亮

## 🔌 API 集成

### 合同审核 API
```typescript
// 执行工作流（直接传递文本）
POST https://ai.hongtai-idi.com/v1/workflows/run
Authorization: Bearer app-4S9HSpKTK7P9Ay7Llc9VUp1e
{
  "inputs": {
    "hetong": "合同文本内容字符串"
  },
  "response_mode": "blocking",
  "user": "用户标识"
}
```

### 知识库问答 API
```typescript
POST https://ai.hongtai-idi.com/v1/chat-messages
Authorization: Bearer app-vfT1is3NTxPKkYaKBRFq7ezV
{
  "query": "用户问题",
  "inputs": {},
  "response_mode": "blocking",
  "user": "用户标识",
  "conversation_id": "会话ID（可选）"
}
```

## 🚀 部署

### Docker 部署
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --production

COPY . .
RUN npm run build

EXPOSE 3000

CMD ["npm", "start"]
```

### Vercel 部署
1. 连接 GitHub 仓库
2. 配置环境变量
3. 自动部署

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [Ant Design 文档](https://ant.design/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [Dify 平台](https://dify.ai/)

---

如有问题或建议，请联系开发团队。