<!DOCTYPE html>
<html>
<head>
    <title>Mermaid测试</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
</head>
<body>
    <h2>测试用例1：错误格式</h2>
    <div id="test1"></div>
    
    <h2>测试用例2：修复后格式</h2>
    <div id="test2"></div>
    
    <script>
        mermaid.initialize({ startOnLoad: false });
        
        // 错误的代码（可能导致您遇到的问题）
        const badCode = `mermaidmermaidgraph TD
A[供应商申请] --> B --> C{准入评估}`;
        
        // 修复后的代码
        const goodCode = `flowchart TD
    A[供应商申请] --> B[资质审核]
    B --> C{准入评估}
    C -->|通过| D[招标与竞争性磋商]
    C -->|不通过| E[信息归档]`;
        
        // 测试错误代码
        try {
            mermaid.render('test1-svg', badCode).then(result => {
                document.getElementById('test1').innerHTML = result.svg;
            }).catch(error => {
                document.getElementById('test1').innerHTML = 
                    `<div style="color: red;">错误: ${error.message}</div>
                     <pre style="background: #f5f5f5; padding: 10px;">${badCode}</pre>`;
            });
        } catch (error) {
            console.error('Test 1 error:', error);
        }
        
        // 测试修复后代码
        try {
            mermaid.render('test2-svg', goodCode).then(result => {
                document.getElementById('test2').innerHTML = result.svg;
            }).catch(error => {
                document.getElementById('test2').innerHTML = 
                    `<div style="color: red;">错误: ${error.message}</div>
                     <pre style="background: #f5f5f5; padding: 10px;">${goodCode}</pre>`;
            });
        } catch (error) {
            console.error('Test 2 error:', error);
        }
    </script>
</body>
</html>